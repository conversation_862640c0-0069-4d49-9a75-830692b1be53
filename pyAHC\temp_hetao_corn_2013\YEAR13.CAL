********************************************************************************
* Filename: YEAR13.CAL
* Contents: AHC 1.0  - crop calendar   
********************************************************************************
*c Rotation scheme 
********************************************************************************

********************************************************************************
* Specify for each crop (maximum 3):
*
* col#1 CRPFIL = Crop data input file without .CRP extension, [A8]
* col#2 Type   = Type of crop model: Simple=1, EPIC=2, WOFOST=3
* col#3 CAPFIL = Input file with calculated irrication parameters
*             without .CAP extension [A8]
* col#4 IF Type=1-3, EMERGENCE = Emergence date of the crop;
*       IF Type=4,   Sowing    = Sowing date of the crop
* col#5 END_crop  = Forced end of crop growth
* col#6 START_sch = Start of irrigation scheduling period
*
**********************************************************************************
* Section 1:Planting and irrigation date
* CRPFIL  Type        CAPFIL   Sowing    END_crop    START_sch  
*                              d1 m1     d2 m2       d3 m3  
'Maize'   2  ''  4  5     21  9     8  10
* End of table
*********************************************************************************

*********************************************************************************
* Section 2:Domancy period
  SWDORM = 0 !   Switch, if considering the dormancy [Y=1, N=0] 
* If SWDORM = 1 specify:  Start and end date
* dd  mm  YY        dd  mm  YY  
   29  12  2010        25  02  2011
************************************************************************************************

************************************************************************************************
* Section 3:Mulching
  SWMULCH = 1 !   Switch, mulch effect [Y=1, N=0]
* If SWMULCH=1 then specify: Start and end date, fmat (mulch material factor) [0.5-1.0, R] and
*                           mfrac (mulch fraction) [0-1.0,R]
* dd  mm  YY     dd  mm  YY     fmat  mfrac
 4    5    2013  25   09   2013  1.0  0.5   
*********************************************************************************
* End of file *******************************************************************
***
*