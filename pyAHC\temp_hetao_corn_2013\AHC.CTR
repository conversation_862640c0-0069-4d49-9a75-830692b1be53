********************************************************************************
* Filename: AHC.CTR
* Contents: AHC v2.0  general input data
********************************************************************************
* HUINONG AREA WATER, SOLUTE AND CROP
* ********************************************************************************

********************************************************************************
* Section 1: Environment
*
  Project = 'hetao' ! Generic name for .SWP, .SLT and .HEA file, [A8]
* Path    = '' ! Path to data directory, [A50]
  CType   = 1 ! Switch, computer type, [PC = 1, VAX & Workstation = 2]
  SWSCRE  = 1 ! Switch, display progression of simulation run [Y=1, N=0]
********************************************************************************

********************************************************************************
* Section 2: Time variables
*
  SSRUN  = 20 5 2013 ! Start date of simulation run, give day month year, [3I]
  ESRUN  = 25 9 2013 ! End date of simulation run, give day month year, [3I]
  FMAY   = 1 ! First month of the agricultural year, Jan.= 1, [1..12, I]
  Period = 1 ! Output interval, ignore = 0, [0..366, I]
  SWRES  = 0 ! reset output interval counter each year, [Y=1, N=0]
  SWODAT = 0! Switch, extra output dates are given in table, [Y=1, N=0]
*
* If SWODAT = 1, table with additional output dates, SWODAT=0时跳过不输
* Date records of type dd mm yyyy (max. 366), [1..31, 1..12, 1..3000, 3I]
 8  4  2013  
 15  4  2013  
 27  4  2013  
 2  5  2013  
 3  5  2013  
 5  5  2013  
 8  5  2013  
 9  5  2013  
 11  5  2013  
 13  5  2013  
 14  5  2013  
 16  5  2013  
 18  5  2013  
 20  5  2013  
 22  5  2013  
 24  5  2013  
 25  5  2013  
 27  5  2013  
 30  5  2013  
 2  6  2013  
 4  6  2013  
 5  6  2013  
 6  6  2013  
 8  6  2013  
 10  6  2013  
 13  6  2013  
 15  6  2013  
 18  6  2013  
 20  6  2013  
 22  6  2013  
 26  6  2013  
 28  6  2013  
 1  7  2013  
 3  7  2013  
 5  7  2013  
 6  7  2013  
 9  7  2013  
 12  7  2013  
* End of table
********************************************************************************

********************************************************************************
* Section 3: Meteorological data
*
  METFIL = 'linhe' ! Filename without extension of meteorological data, [A7]
  LAT    = 45.75 ! Latitude of meteo station [-90..90 degrees, R, North = +]
  ALT    = 1039.3 ! Altitude of meteo station [-400..3000 m, R]
  WINAL  = 2.0 ! Height of wind speed measurement above soil surface[0..99 m, R]
  SWDIVPM = 1 ! Switch, simulation of potential E and T
*            1 = Calculate PE and PT based on crop and soil factors
*            2 = Reseved method 1
*            3 = Reseved method 2
*            4 = Use Hargreaves equation to calculate ETo
  SWETR  = 0 ! Switch, use ETRef values from meteo data file
  SWRAI  = 0 ! Switch, use detailed rainfall data [Y=1, N=0]
  SWRAD  =1 ! Switch, choose the type of measured radiation data
*            1 = Using global solar radiation
*            2 = Use the daily sunshine hours
  SWHUM  =1 ! Switch, choose the type of measured humidity data
*            1 = Use vapor pressure
*            2 = Use relative humidity
********************************************************************************

********************************************************************************
* Section 4: In- and output files for the simulation runs
*
* Specify for each simulation run (max. 30) the following 5 file names
* without extension (no file: write '  ' )
* 1 - the (optional) input file with fixed irrigation data [.IRG]
* 2 - the (optional) input file with the crop rotation scheme [.CAL]
* 3 - the (optional) input file for the basic drainage routine [.DRB]
*  or the (optional) input file for the extended drainage routine [.DRE]
* 4 - the input file with the bottom boundary condition [.BBC]
* 5 - generic name of output files
*
*   IRGFIL     CALFIL      DRFIL     BBCFIL     OUTFIL
 '   hetao' '  YEAR13' '   hetao' '   hetao' '  rs0' 
********************************************************************************

********************************************************************************
* Section 5: Processes which should be considered
* 
  SWSWF  = 1 ! Switch, simulation of soil water flow [Y=1, N=0] 用于溶质解析解cxu
*
  SWDRA  = 1 ! Switch, simulation of lateral drainage:
*            0 = No simulation of drainage
*            1 = Simulation with basic drainage routine
*            2 = Simulation with extended drainage routine
*
  SWSOLU = 1 ! Switch, simulation of solute transport [Y=1, N=0]
* If SWSOLU = 1 specify:
     SWSTYP = 1 ! Choose solute type [=1,2,3]
*              1 = Salinity, total solute transport 
*              2 = Nitrogen transport
*              3 = Multicomponent solute transport
*              4 = Nitrogen & Salinity transport
  SWHEA  = 0 ! Switch, simulation of soil heat transport [Y=1, N=0]
  SWVAPT = 0 ! Switch, considering vapor transfer [Y=1, N=0]
********************************************************************************

********************************************************************************
* Section 6: Optional output files, each run generates a separate file
*
  SWVAP = 1 ! Switch, output profiles of moisture solute and temperature [Y=1, N=0]
  SWDRF = 0 ! Switch, output drainage fluxes, only for extended drainage [Y=1, N=0]
  SWSWB = 0 ! Switch, output surface water reservoir, only for ext. dr. [Y=1, N=0]
********************************************************************************

********************************************************************************
* Section 7: Output balance zone from node #1 to node #BALNODE
* 定义了水盐均衡计算剖面的底部节点
  BALNODE =200 ! bottom node of balance zone, [=0..500; N=0 means no output]
********************************************************************************

********************************************************************************
* Section 8: Optional for model running [=0,1,2]
  iclb = 0 !      0 = normal run 
*                    1 = do sensitivity analysis
*                    2 = do model calibration
*                    3 = GA run
* End of File ******************************************************************
