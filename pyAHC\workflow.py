"""HDF5集成工作流程模块

提供完整的HDF5集成工作流程，包括项目初始化、日循环模拟流程、项目汇总功能等。
将前面实现的各个组件整合成一个完整的、可用的系统。
"""

import logging
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import numpy as np

from pyahc.db.hdf5.manager import ProjectHDF5Manager
from pyahc.db.hdf5.state_extractor import StateExtractor
from pyahc.db.hdf5.state_injector import StateInjector
from pyahc.db.hdf5.exceptions import HDF5Error, ProjectNotFoundError
from pyahc.db.hdf5.assimilation import AssimilationManager


def initialize_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """初始化项目数据同化模拟
    
    Args:
        config: 项目配置字典，包含以下键：
            - hdf5_path: HDF5文件路径
            - project_name: 项目名称（如 "corn_001-2013"）
            - start_date: 开始日期
            - end_date: 结束日期
            - field_id: 地块ID
            - crop_type: 作物类型
            - latitude, longitude, altitude: 位置信息
            - 其他可选参数
    
    Returns:
        初始化完成的ProjectHDF5Manager实例
    """
    try:
        # 验证配置参数
        validated_config = _validate_project_config(config)
        
        # 创建HDF5管理器
        hdf5_path = Path(validated_config['hdf5_path'])
        manager = ProjectHDF5Manager(hdf5_path)
        
        # 项目配置
        project_name = validated_config['project_name']
        
        # 检查项目是否已存在
        if manager.project_exists(project_name):
            logging.warning(f"Project {project_name} already exists, skipping creation")
            return manager
        
        # 生成项目元数据
        metadata = _generate_project_metadata(validated_config)
        
        # 创建项目
        with manager:
            manager.create_project(project_name, metadata)
        
        logging.info(f"Successfully initialized project: {project_name}")
        return manager
        
    except Exception as e:
        logging.error(f"Failed to initialize project simulation: {e}")
        raise HDF5Error(f"Project initialization failed: {e}")


def _validate_project_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """验证项目配置参数"""
    required_keys = ['hdf5_path', 'project_name', 'start_date', 'end_date']
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    # 验证日期格式
    start_date = config['start_date']
    end_date = config['end_date']
    
    if isinstance(start_date, str):
        config['start_date'] = datetime.fromisoformat(start_date).date()
    elif isinstance(start_date, datetime):
        config['start_date'] = start_date.date()
    
    if isinstance(end_date, str):
        config['end_date'] = datetime.fromisoformat(end_date).date()
    elif isinstance(end_date, datetime):
        config['end_date'] = end_date.date()
    
    # 验证日期逻辑
    if config['start_date'] >= config['end_date']:
        raise ValueError("Start date must be before end date")
    
    # 设置默认值
    config.setdefault('field_id', 'unknown')
    config.setdefault('crop_type', 'unknown')
    config.setdefault('latitude', None)
    config.setdefault('longitude', None)
    config.setdefault('altitude', None)
    config.setdefault('variety', 'unknown')
    config.setdefault('planting_date', None)
    
    return config


def _generate_project_metadata(config: Dict[str, Any]) -> Dict[str, Any]:
    """生成项目元数据"""
    metadata = {
        'model_info': {
            'pyahc_version': '0.1.0',  # 应该从实际版本获取
            'ahc_version': 'V201',
            'creation_date': datetime.now().isoformat(),
            'created_by': 'pyAHC HDF5 Integration'
        },
        'simulation_period': {
            'start_date': config['start_date'].isoformat(),
            'end_date': config['end_date'].isoformat(),
            'total_days': (config['end_date'] - config['start_date']).days + 1
        },
        'location_info': {
            'field_id': config['field_id'],
            'latitude': config['latitude'],
            'longitude': config['longitude'],
            'altitude': config['altitude']
        },
        'crop_info': {
            'crop_type': config['crop_type'],
            'variety': config['variety'],
            'planting_date': config['planting_date'].isoformat() if config['planting_date'] else ''
        },
        'configuration': {
            'enable_assimilation': config.get('enable_assimilation', False),
            'stop_on_error': config.get('stop_on_error', True),
            'save_intermediate': config.get('save_intermediate', True)
        }
    }
    
    return metadata


def daily_simulation_step(manager: ProjectHDF5Manager,
                         project_name: str,
                         current_date: date,
                         base_model: 'Model') -> 'Result':
    """执行单日模拟步骤
    
    Args:
        manager: HDF5管理器
        project_name: 项目名称
        current_date: 当前模拟日期
        base_model: 基础模型对象
        
    Returns:
        模型运行结果
    """
    try:
        # 1. 准备当日模型（深拷贝）
        daily_model = base_model.model_copy(deep=True)
        
        # 2. 加载前一日状态（如果存在）
        previous_date = current_date - timedelta(days=1)
        previous_states = _load_previous_states(manager, project_name, previous_date)
        
        # 3. 注入前一日状态到当日模型
        if previous_states:
            daily_model = StateInjector.inject_all_states(daily_model, previous_states)
            logging.info(f"Loaded and injected states from {previous_date}")
        else:
            logging.info(f"No previous states found for {previous_date}, using initial conditions")
        
        # 4. 更新模拟日期
        _update_simulation_date(daily_model, current_date)
        
        # 5. 运行模型
        result = _run_daily_model(daily_model, current_date)
        
        # 6. 提取当日状态变量
        current_states = StateExtractor.extract_all_states(result)
        
        # 7. 提取参数变量（如果需要）
        current_parameters = _extract_parameters_from_model(daily_model)
        
        # 8. 保存所有数据到HDF5
        with manager:
            manager.save_daily_data(
                project_name, current_date, daily_model, result,
                current_states, current_parameters
            )
        
        logging.info(f"Completed daily simulation for {project_name}/{current_date}")
        return result
        
    except Exception as e:
        logging.error(f"Daily simulation failed for {current_date}: {e}")
        raise


def _load_previous_states(manager: ProjectHDF5Manager, 
                         project_name: str, 
                         previous_date: date) -> Optional[Dict[str, Any]]:
    """加载前一日状态变量"""
    try:
        with manager:
            daily_data = manager.load_daily_data(project_name, previous_date)
            return daily_data.get('states', {})
    except Exception as e:
        logging.debug(f"Could not load previous states for {previous_date}: {e}")
        return None


def _update_simulation_date(model: 'Model', current_date: date) -> None:
    """更新模型的模拟日期"""
    try:
        if hasattr(model, 'generalsettings') and model.generalsettings is not None:
            model.generalsettings.tstart = current_date
            model.generalsettings.tend = current_date
        elif hasattr(model, 'settings'):
            model.settings.start_date = current_date
            model.settings.end_date = current_date
        else:
            logging.warning("Could not find date settings in model")
    except Exception as e:
        logging.error(f"Failed to update simulation date: {e}")


def _run_daily_model(model: 'Model', current_date: date) -> 'Result':
    """运行日模型"""
    try:
        result = model.run()
        if result is None:
            raise RuntimeError("Model run returned None")
        return result
    except Exception as e:
        logging.error(f"Model run failed for {current_date}: {e}")
        raise


def _extract_parameters_from_model(model: 'Model') -> Dict[str, Any]:
    """从模型中提取参数变量"""
    parameters = {}
    
    try:
        # 提取土壤参数
        if hasattr(model, 'soilprofile') and model.soilprofile is not None:
            if hasattr(model.soilprofile, 'hydraulic_conductivity'):
                parameters['hydraulic_conductivity'] = model.soilprofile.hydraulic_conductivity
            if hasattr(model.soilprofile, 'porosity'):
                parameters['porosity'] = model.soilprofile.porosity
        
        # 提取作物参数
        if hasattr(model, 'crop') and model.crop is not None:
            if hasattr(model.crop, 'crop_coefficient'):
                parameters['crop_coefficient'] = model.crop.crop_coefficient
            if hasattr(model.crop, 'rooting_depth_max'):
                parameters['rooting_depth_max'] = model.crop.rooting_depth_max
        
        # 提取气象参数
        if hasattr(model, 'meteorology') and model.meteorology is not None:
            if hasattr(model.meteorology, 'reference_et'):
                parameters['reference_et'] = model.meteorology.reference_et
        
    except Exception as e:
        logging.warning(f"Failed to extract some parameters: {e}")
    
    return parameters


def generate_project_summary(manager: ProjectHDF5Manager,
                           project_name: str,
                           start_date: date,
                           end_date: date) -> None:
    """生成项目汇总数据

    Args:
        manager: HDF5管理器
        project_name: 项目名称
        start_date: 开始日期
        end_date: 结束日期
    """
    try:
        with manager:
            project_group = manager._file[project_name]

            # 创建或获取summary组
            if 'summary' not in project_group:
                summary_group = project_group.create_group('summary')
                timeseries_group = summary_group.create_group('timeseries')
                statistics_group = summary_group.create_group('statistics')
            else:
                summary_group = project_group['summary']
                timeseries_group = summary_group['timeseries']
                statistics_group = summary_group['statistics']

            # 收集时间序列数据
            timeseries_data = _collect_timeseries_data(manager, project_name, start_date, end_date)

            # 保存时间序列数据
            _save_timeseries_data(timeseries_group, timeseries_data)

            # 计算和保存统计信息
            statistics = _calculate_project_statistics(timeseries_data)
            _save_statistics(statistics_group, statistics)

        logging.info(f"Generated summary for project {project_name}")

    except Exception as e:
        logging.error(f"Failed to generate project summary: {e}")
        raise


def _collect_timeseries_data(manager: ProjectHDF5Manager,
                           project_name: str,
                           start_date: date,
                           end_date: date) -> Dict[str, List]:
    """收集时间序列数据"""
    timeseries_data = {
        'dates': [],
        'lai': [],
        'biomass': [],
        'soil_moisture': [],
        'root_depth': [],
        'groundwater_level': []
    }

    current_date = start_date
    while current_date <= end_date:
        try:
            daily_data = manager.load_daily_data(project_name, current_date)
            states = daily_data.get('states', {})

            timeseries_data['dates'].append(current_date.isoformat())
            timeseries_data['lai'].append(states.get('lai', np.nan))
            timeseries_data['biomass'].append(states.get('biomass', np.nan))
            timeseries_data['root_depth'].append(states.get('root_depth', np.nan))
            timeseries_data['groundwater_level'].append(states.get('groundwater_level', np.nan))

            # 处理土壤含水量（可能是数组）
            soil_moisture = states.get('soil_moisture')
            if soil_moisture is not None:
                if isinstance(soil_moisture, np.ndarray):
                    timeseries_data['soil_moisture'].append(soil_moisture)
                else:
                    timeseries_data['soil_moisture'].append(np.array([soil_moisture]))
            else:
                timeseries_data['soil_moisture'].append(np.array([np.nan]))

        except Exception as e:
            logging.debug(f"No data for {current_date}: {e}")
            # 填充缺失数据
            timeseries_data['dates'].append(current_date.isoformat())
            timeseries_data['lai'].append(np.nan)
            timeseries_data['biomass'].append(np.nan)
            timeseries_data['root_depth'].append(np.nan)
            timeseries_data['groundwater_level'].append(np.nan)
            timeseries_data['soil_moisture'].append(np.array([np.nan]))

        current_date += timedelta(days=1)

    return timeseries_data


def _save_timeseries_data(timeseries_group, timeseries_data: Dict[str, List]) -> None:
    """保存时间序列数据到HDF5"""
    # 保存日期信息
    dates_encoded = [d.encode('utf-8') for d in timeseries_data['dates']]

    # 保存LAI时间序列
    if 'lai_timeseries' in timeseries_group:
        del timeseries_group['lai_timeseries']
    lai_dataset = timeseries_group.create_dataset(
        'lai_timeseries',
        data=np.array(timeseries_data['lai']),
        compression='gzip'
    )
    lai_dataset.attrs['units'] = 'm2/m2'
    lai_dataset.attrs['dates'] = dates_encoded

    # 保存生物量时间序列
    if 'biomass_timeseries' in timeseries_group:
        del timeseries_group['biomass_timeseries']
    biomass_dataset = timeseries_group.create_dataset(
        'biomass_timeseries',
        data=np.array(timeseries_data['biomass']),
        compression='gzip'
    )
    biomass_dataset.attrs['units'] = 'kg/ha'
    biomass_dataset.attrs['dates'] = dates_encoded

    # 保存土壤含水量时间序列
    if 'moisture_timeseries' in timeseries_group:
        del timeseries_group['moisture_timeseries']

    # 处理可变长度的土壤含水量数据
    max_layers = max(len(sm) for sm in timeseries_data['soil_moisture'])
    moisture_array = np.full((len(timeseries_data['soil_moisture']), max_layers), np.nan)

    for i, sm in enumerate(timeseries_data['soil_moisture']):
        moisture_array[i, :len(sm)] = sm

    moisture_dataset = timeseries_group.create_dataset(
        'moisture_timeseries',
        data=moisture_array,
        compression='gzip'
    )
    moisture_dataset.attrs['units'] = 'cm3/cm3'
    moisture_dataset.attrs['dates'] = dates_encoded
    moisture_dataset.attrs['max_layers'] = max_layers


def _calculate_project_statistics(timeseries_data: Dict[str, List]) -> Dict[str, float]:
    """计算项目统计信息"""
    statistics = {}

    # LAI统计
    lai_array = np.array(timeseries_data['lai'])
    if len(lai_array) > 0 and not np.all(np.isnan(lai_array)):
        statistics['lai_max'] = float(np.nanmax(lai_array))
        statistics['lai_mean'] = float(np.nanmean(lai_array))
        statistics['lai_min'] = float(np.nanmin(lai_array))

    # 生物量统计
    biomass_array = np.array(timeseries_data['biomass'])
    if len(biomass_array) > 0 and not np.all(np.isnan(biomass_array)):
        statistics['biomass_max'] = float(np.nanmax(biomass_array))
        statistics['biomass_mean'] = float(np.nanmean(biomass_array))
        statistics['biomass_final'] = float(biomass_array[-1]) if not np.isnan(biomass_array[-1]) else np.nan

    # 根深统计
    root_array = np.array(timeseries_data['root_depth'])
    if len(root_array) > 0 and not np.all(np.isnan(root_array)):
        statistics['root_depth_max'] = float(np.nanmax(root_array))
        statistics['root_depth_mean'] = float(np.nanmean(root_array))

    # 地下水位统计
    gw_array = np.array(timeseries_data['groundwater_level'])
    if len(gw_array) > 0 and not np.all(np.isnan(gw_array)):
        statistics['groundwater_level_mean'] = float(np.nanmean(gw_array))
        statistics['groundwater_level_min'] = float(np.nanmin(gw_array))

    # 土壤含水量统计
    moisture_data = timeseries_data['soil_moisture']
    if moisture_data:
        valid_moisture_arrays = [sm[~np.isnan(sm)] for sm in moisture_data if len(sm[~np.isnan(sm)]) > 0]
        if valid_moisture_arrays:
            all_moisture = np.concatenate(valid_moisture_arrays)
            if len(all_moisture) > 0:
                statistics['soil_moisture_mean'] = float(np.mean(all_moisture))
                statistics['soil_moisture_std'] = float(np.std(all_moisture))

    return statistics


def _save_statistics(statistics_group, statistics: Dict[str, float]) -> None:
    """保存统计信息到HDF5"""
    for key, value in statistics.items():
        if not np.isnan(value):
            statistics_group.attrs[key] = value


def run_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """运行完整的项目模拟

    Args:
        config: 项目配置字典

    Returns:
        完成模拟的ProjectHDF5Manager实例
    """
    try:
        # 1. 初始化项目
        manager = initialize_project_simulation(config)
        project_name = config['project_name']

        # 2. 获取模拟参数
        start_date = config['start_date']
        end_date = config['end_date']
        total_days = (end_date - start_date).days + 1

        # 3. 创建基础模型
        base_model = _create_base_model(config)

        # 4. 执行日循环模拟
        logging.info(f"Starting project simulation: {project_name}")
        logging.info(f"Period: {start_date} to {end_date} ({total_days} days)")

        current_date = start_date
        day_count = 0

        while current_date <= end_date:
            try:
                # 执行单日模拟
                result = daily_simulation_step(
                    manager, project_name, current_date, base_model
                )

                # 可选：数据同化（预留接口）
                if config.get('enable_assimilation', False):
                    _apply_data_assimilation(manager, project_name, current_date, config)

                day_count += 1

                # 进度报告
                if day_count % 10 == 0 or day_count == total_days:
                    progress = (day_count / total_days) * 100
                    logging.info(f"Progress: {day_count}/{total_days} days ({progress:.1f}%)")

            except Exception as e:
                logging.error(f"Simulation failed on {current_date}: {e}")
                if config.get('stop_on_error', True):
                    raise
                else:
                    logging.warning(f"Skipping {current_date} and continuing")

            current_date += timedelta(days=1)

        # 5. 生成项目汇总
        generate_project_summary(manager, project_name, start_date, end_date)

        logging.info(f"Project simulation completed successfully: {project_name}")
        return manager

    except Exception as e:
        logging.error(f"Project simulation failed: {e}")
        raise


def _create_base_model(config: Dict[str, Any]) -> 'Model':
    """创建基础模型对象"""
    # 这里应该调用现有的模型创建函数
    # 具体实现取决于pyAHC的模型创建接口
    try:
        # 导入必要的组件
        from pyahc.model.model import Model
        from pyahc.components.simsettings import GeneralSettings
        from pyahc.components.meteorology import Meteorology
        from pyahc.components.crop import Crop
        from pyahc.components.irrigation import FixedIrrigation
        from pyahc.components.soilwater import SoilMoisture, SurfaceFlow, Evaporation, SoilProfile, SnowAndFrost
        from pyahc.components.simsettings import RichardsSettings
        from pyahc.components.drainage import Drainage
        from pyahc.components.boundary import BottomBoundary
        from pyahc.components.transport import HeatFlow, SoluteTransport
        from pyahc.components.metadata import Metadata
        from pyahc.components.ctr import CtrFile

        # 创建基本设置
        general_settings = GeneralSettings(
            project=config.get('project_name', 'HDF5_Simulation'),
            pathwork=str(Path.cwd()),
            swscre=0,
            swerror=1,
            extensions=["csv"],
            tstart=config['start_date'],
            tend=config['end_date']
        )

        # 创建基本组件（使用默认值，可以根据配置调整）
        metadata = Metadata()

        # 创建CtrFile with required fields
        ctr_file = CtrFile(
            project=config.get('project_name', 'HDF5_Simulation'),
            ssrun=[config['start_date'].day, config['start_date'].month, config['start_date'].year],
            esrun=[config['end_date'].day, config['end_date'].month, config['end_date'].year],
            metfil='weather.013',  # 默认气象文件名
            lat=config.get('latitude', 40.0),
            alt=config.get('altitude', 0.0)
        )

        meteorology = Meteorology()
        crop = Crop()  # 启用作物模拟
        fixedirrigation = FixedIrrigation(swirfix=0)
        soilmoisture = SoilMoisture()
        surfaceflow = SurfaceFlow()
        evaporation = Evaporation()
        soilprofile = SoilProfile()
        snowandfrost = SnowAndFrost(swsnow=0, swfrost=0)
        richards = RichardsSettings(swkmean=1, swkimpl=0)
        lateraldrainage = Drainage()
        bottomboundary = BottomBoundary(
            swbbcfile=0,  # 不写入单独的BBC文件
            swbotb=6,     # 底部通量为零（简单的默认选项）
        )
        heatflow = HeatFlow(swhea=0)
        solutetransport = SoluteTransport(swsolu=0)

        # 创建模型
        base_model = Model(
            metadata=metadata,
            ctr_file=ctr_file,
            generalsettings=general_settings,
            meteorology=meteorology,
            crop=crop,
            fixedirrigation=fixedirrigation,
            soilmoisture=soilmoisture,
            surfaceflow=surfaceflow,
            evaporation=evaporation,
            soilprofile=soilprofile,
            snowandfrost=snowandfrost,
            richards=richards,
            lateraldrainage=lateraldrainage,
            bottomboundary=bottomboundary,
            heatflow=heatflow,
            solutetransport=solutetransport
        )

        # 根据配置调整模型参数
        if 'crop_type' in config:
            # 设置作物类型相关参数
            logging.info(f"Setting crop type: {config['crop_type']}")

        if 'field_id' in config:
            # 设置地块相关参数
            logging.info(f"Setting field ID: {config['field_id']}")

        return base_model

    except Exception as e:
        logging.error(f"Failed to create base model: {e}")
        raise


def run_optimized_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """运行优化的项目模拟

    使用优化引擎运行项目模拟，提供更好的性能和稳定性。

    Args:
        config: 项目配置字典，包含优化相关参数：
            - 基础配置（与run_project_simulation相同）
            - optimization: 优化配置字典
                - memory_limit_mb: 内存限制（MB）
                - gc_frequency: 垃圾回收频率
                - checkpoint_frequency: 检查点保存频率
                - enable_parallel: 是否启用并行处理
                - checkpoint_dir: 检查点目录

    Returns:
        完成模拟的ProjectHDF5Manager实例
    """
    try:
        # 检查是否启用优化
        if not config.get('use_optimization', False):
            logging.info("Optimization not enabled, using standard simulation")
            return run_project_simulation(config)

        # 导入优化引擎
        from .pyahc.db.hdf5.optimization import OptimizedSimulationEngine

        # 1. 初始化项目
        manager = initialize_project_simulation(config)
        project_name = config['project_name']

        # 2. 获取模拟参数
        start_date = config['start_date']
        end_date = config['end_date']
        total_days = (end_date - start_date).days + 1

        # 3. 创建基础模型
        base_model = _create_base_model(config)

        # 4. 创建优化引擎
        optimization_config = config.get('optimization', {})
        # 合并全局配置和优化配置
        engine_config = {**config, **optimization_config}
        engine = OptimizedSimulationEngine(engine_config)

        # 5. 执行优化模拟
        logging.info(f"Starting optimized project simulation: {project_name}")
        logging.info(f"Period: {start_date} to {end_date} ({total_days} days)")
        logging.info(f"Optimization settings: memory_limit={engine_config.get('memory_limit_mb', 2048)}MB, "
                    f"gc_freq={engine_config.get('gc_frequency', 10)}, "
                    f"checkpoint_freq={engine_config.get('checkpoint_frequency', 50)}")

        engine.run_optimized_simulation(manager, project_name, start_date, end_date, base_model)

        # 6. 生成项目汇总
        logging.info("Generating project summary...")
        generate_project_summary(manager, project_name)

        logging.info(f"Optimized project simulation completed: {project_name}")
        return manager

    except Exception as e:
        logging.error(f"Optimized project simulation failed: {e}")
        raise


def run_batch_simulation(base_config: Dict[str, Any],
                        parameter_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
    """运行批量模拟

    执行参数扫描和批量模拟。

    Args:
        base_config: 基础配置字典
        parameter_ranges: 参数范围字典，例如：
            {
                'irrigation_efficiency': [0.7, 0.8, 0.9, 1.0],
                'fertilizer_rate': [100, 150, 200, 250]
            }

    Returns:
        批量模拟结果列表
    """
    try:
        # 导入批量模拟管理器
        from .pyahc.db.hdf5.optimization import BatchSimulationManager

        # 创建批量模拟管理器
        batch_config = base_config.get('batch', {})
        manager_config = {**base_config, **batch_config}
        batch_manager = BatchSimulationManager(manager_config)

        logging.info(f"Starting batch simulation with {len(parameter_ranges)} parameter types")

        # 计算总组合数
        import itertools
        total_combinations = 1
        for param_values in parameter_ranges.values():
            total_combinations *= len(param_values)

        logging.info(f"Total parameter combinations: {total_combinations}")

        # 运行参数扫描
        results = batch_manager.run_parameter_sweep(base_config, parameter_ranges)

        # 统计结果
        successful_runs = sum(1 for r in results if r.get('success', False))
        failed_runs = len(results) - successful_runs

        logging.info(f"Batch simulation completed: {successful_runs} successful, {failed_runs} failed")

        return results

    except Exception as e:
        logging.error(f"Batch simulation failed: {e}")
        raise


def _apply_data_assimilation(manager: ProjectHDF5Manager,
                           project_name: str,
                           current_date: date,
                           config: Dict[str, Any]) -> None:
    """应用数据同化"""
    try:
        # 检查是否启用数据同化
        if not config.get('enable_assimilation', False):
            return

        # 初始化数据同化管理器
        assimilation_manager = AssimilationManager(config)

        # 执行日数据同化
        success = assimilation_manager.process_daily_assimilation(
            manager, project_name, current_date
        )

        if success:
            logging.info(f"Data assimilation completed successfully for {current_date}")
        else:
            logging.debug(f"No data assimilation performed for {current_date}")

    except Exception as e:
        logging.warning(f"Data assimilation failed for {current_date}: {e}")


def _load_observations_for_date(date: date, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """加载指定日期的观测数据（已弃用，由AssimilationManager处理）"""
    # 此函数已被AssimilationManager.observation_loader替代
    # 保留以维持向后兼容性
    logging.warning("_load_observations_for_date is deprecated, use AssimilationManager instead")
    return None
