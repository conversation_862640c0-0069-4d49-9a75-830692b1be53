# mypy: disable-error-code="call-overload, misc, override"
# 豁免理由：
# - call-overload 和 misc 在我解包 Field() 调用中的值时引发（例如，**_UNITRANGE）。这种方法是正确的。
# - override 在 model_string 上引发，因为这些方法不共享相同的签名。这不是一个优先修复的问题。
"""边界条件设置。

类：

    BottomBoundary: 底部边界设置（重构版本 - 支持外部参数配置）。
"""

# 使用 pySWAP-main 导入别名约定，带下划线前缀
from pathlib import Path as _Path
from typing import Literal as _Literal, List as _List, Tuple as _Tuple, Optional as _Optional, Dict as _Dict, Any as _Any

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
    model_validator as _model_validator,
)

from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

# 常量定义
MIN_DAY = 1
MAX_DAY = 31
MIN_MONTH = 1
MAX_MONTH = 12
MIN_GROUNDWATER_LEVEL = 0  # 地下水位必须 <= 0
MIN_CONCENTRATION = 0  # 浓度必须 >= 0


class BottomBoundary(
    _ConfigurableComponent, _YAMLValidatorMixin, _FileMixin, _SerializableMixin
):
    """重构后的底部边界组件 - 支持外部参数配置。

    该类实现了底部边界条件的配置和管理，移除了硬编码参数，
    支持外部参数配置和条件验证。

    主要改进：
    - 移除硬编码的 SHAPE, HDRAIN, RIMLAY, AQAVE, AQAMP, AQTAMX, AQPER 等参数
    - 实现参数外部化配置
    - 添加基于 swbotb 开关的条件验证
    - 集成增强的验证框架
    - 支持数据一致性验证

    属性：
        swbbcfile: 是否写入.bbc文件 [0=当前文件, 1=单独文件]
        bbcfil: 底边界文件名
        swbotb: 底部边界条件类型 [1-8]
            1 - 规定地下水位
            2 - 规定底部通量
            3 - 根据深层含水层的压头计算底部通量
            4 - 根据地下水位计算底部通量
            5 - 规定底部隔室的土壤水压头
            6 - 底部通量为零
            7 - 土壤剖面自由排水
            8 - 土壤-空气界面自由流出
    """

    _extension = _PrivateAttr(default="BBC")

    # 基础开关参数
    swbbcfile: _Literal[0, 1] = _Field(description="是否写入.bbc文件")
    bbcfil: _Optional[str] = _Field(default=None, description="底边界文件名")

    # 边界条件开关（外部化配置）
    swbotb: _Optional[_Literal[1, 2, 3, 4, 5, 6, 7, 8]] = _Field(default=None, description="底部边界条件类型")

    # 深层含水层参数（可配置 ）
    shape: _Optional[float] = _Field(default=None, description="形状因子")
    hdrain: _Optional[float] = _Field(default=None, description="排水基础")
    rimlay: _Optional[float] = _Field(default=None, description="隔水层垂直阻力")
    aqave: _Optional[float] = _Field(default=None, description="平均水力水头差")
    aqamp: _Optional[float] = _Field(default=None, description="水力水头正弦波振幅")
    aqtamx: _Optional[int] = _Field(default=None, description="最大水力水头日数")
    aqper: _Optional[int] = _Field(default=None, description="水力水头正弦波周期")

    # 地下水位函数参数
    cofqha: _Optional[float] = _Field(default=None, description="系数A")
    cofqhb: _Optional[float] = _Field(default=None, description="系数B")
    cofqhc: _Optional[float] = _Field(default=None, description="系数C")

    # 其他开关参数（外部化配置）
    sw2: _Optional[_Literal[1, 2]] = _Field(default=None, description="底部通量函数类型")
    sw3: _Optional[_Literal[1, 2]] = _Field(default=None, description="深层含水层水头类型")
    swqhbot: _Optional[_Literal[1, 2]] = _Field(default=None, description="地下水位函数类型")

    # 正弦函数参数
    sinave: _Optional[float] = _Field(default=None, description="底部通量平均值")
    sinamp: _Optional[float] = _Field(default=None, description="底部通量振幅")
    sinmax: _Optional[int] = _Field(default=None, description="底部通量最大值日数")

    # 数据表（外部化配置）
    gwlevel_data: _Optional[_List[_Tuple[int, int, float]]] = _Field(
        default=None, description="地下水位数据"
    )
    gwc_data: _Optional[_List[_Tuple[int, int, float]]] = _Field(
        default=None, description="地下水浓度数据"
    )
    hbot5_data: _Optional[_List[_Tuple[int, int, float]]] = _Field(
        default=None, description="底部隔室压头数据"
    )

    # 开关参数（外部化配置）
    swopt1_value: _Optional[int] = _Field(default=None, description="地下水位选项")
    swopt2_value: _Optional[int] = _Field(default=None, description="底部通量选项")
    swopt3_value: _Optional[int] = _Field(default=None, description="深层含水层选项")
    swopt4_value: _Optional[int] = _Field(default=None, description="地下水位函数选项")
    swopt5_value: _Optional[int] = _Field(default=None, description="底部隔室压头选项")
    swopt6_value: _Optional[int] = _Field(default=None, description="底部通量为零选项")
    swopt7_value: _Optional[int] = _Field(default=None, description="自由排水选项")
    swopt8_value: _Optional[int] = _Field(default=None, description="自由流出选项")

    # 地下水浓度开关
    swvgwc: _Optional[int] = _Field(default=None, description="地下水浓度开关")

    # 热模拟参数
    swhopt1: _Optional[int] = _Field(default=None, description="底部温度选项")
    swhopt2: _Optional[int] = _Field(default=None, description="底部热通量选项")
    swvhcs: _Optional[int] = _Field(default=None, description="可变热导率选项")

    # 表层土壤调整参数
    swhpadj: _Optional[int] = _Field(default=None, description="表层土壤调整选项")
    depadj: _Optional[float] = _Field(default=None, description="表层土壤深度")

    def __init__(self, **data):
        super().__init__(**data)

    def _check_required_parameters(self, params: _List[str], condition: str) -> None:
        """检查必需参数是否存在"""
        missing = []
        for param in params:
            value = getattr(self, param)
            if value is None:
                missing.append(param)
        if missing:
            raise ValueError(f"{condition} requires: {missing}")

    @_model_validator(mode="after")
    def validate_conditional_parameters(self):
        """验证条件参数"""
        if not self._validation:
            return self

        # swbotb 条件验证
        if self.swbotb == 1:  # 规定地下水位
            if not self.gwlevel_data:
                raise ValueError("swbotb=1 requires gwlevel_data")

        elif self.swbotb == 3:  # 深层含水层选项
            required_params = ['shape', 'hdrain', 'rimlay', 'aqave', 'aqamp', 'aqtamx', 'aqper']
            self._check_required_parameters(required_params, "swbotb=3")

        elif self.swbotb == 4:  # 地下水位函数选项
            required_params = ['cofqha', 'cofqhb', 'cofqhc']
            self._check_required_parameters(required_params, "swbotb=4")

        elif self.swbotb == 5:  # 底部隔室压头
            if not self.hbot5_data:
                raise ValueError("swbotb=5 requires hbot5_data")

        return self

    def _validate_date_tuple(self, day: int, month: int, data_type: str) -> None:
        """验证日期元组的有效性"""
        if not (MIN_DAY <= day <= MAX_DAY and MIN_MONTH <= month <= MAX_MONTH):
            raise ValueError(f"Invalid date in {data_type}: {day}/{month}")

    @_model_validator(mode="after")
    def validate_data_consistency(self):
        """验证数据一致性"""
        if not self._validation:
            return self

        # 验证地下水位数据
        if self.gwlevel_data:
            for day, month, level in self.gwlevel_data:
                self._validate_date_tuple(day, month, "groundwater level data")
                if level > MIN_GROUNDWATER_LEVEL:
                    raise ValueError(f"Groundwater level must be <= {MIN_GROUNDWATER_LEVEL}, got {level}")

        # 验证地下水浓度数据
        if self.gwc_data:
            for day, month, conc in self.gwc_data:
                self._validate_date_tuple(day, month, "groundwater concentration data")
                if conc < MIN_CONCENTRATION:
                    raise ValueError(f"Concentration must be >= {MIN_CONCENTRATION}, got {conc}")

        # 验证底部隔室压头数据
        if self.hbot5_data:
            for day, month, _ in self.hbot5_data:  # 使用 _ 忽略未使用的 head 变量
                self._validate_date_tuple(day, month, "bottom compartment head data")

        return self

    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "BottomBoundary":
        """从外部配置创建实例"""
        return cls.from_external_config(config)

    def get_effective_param_value(self, param_name: str) -> _Any:
        """获取有效参数值（用于序列化）"""
        return getattr(self, param_name)

    def bbc(self) -> str:
        """返回表示 bbc 文件的字符串。"""
        return self._generate_bbc_content()

    def _generate_bbc_content(self) -> str:
        """生成严格匹配 V201 格式的 BBC 文件内容。"""
        lines = []
        
        # 文件头部 - 严格按照示例文件格式
        lines.append("*" * 80)
        lines.append("* Filename: ")
        lines.append("* Contents: AHC 1.0 - Bottom Boundary Condition ")
        lines.append("*" * 80)
        lines.append("* cGroundwater level is given")
        lines.append("*" * 80)
        lines.append("* Section 1 choose bottom boundary condition")
        lines.append("*")
        lines.append("* Choose one of 8 options")
        lines.append("*")
        lines.append("*")
        
        # SWOPT1 - 地下水位选项 - 使用外部配置参数
        swopt1_val = self.swopt1_value
        lines.append(f"  SWOPT1 = {swopt1_val} !  Switch, use groundwater level, [Y=1, N=0]")
        lines.append("* ")

        # 即使SWOPT1=0，也要包含地下水位数据（按示例文件格式）
        lines.append("* If SWOPT1 = 1 specify date [day month] and groundwater level [-1.E5..100 cm]:")
        lines.append("* d1 m1  GWlevel        (maximum 366 records)")

        # 地下水位数据 - 使用外部配置数据
        gwlevel_data = self.gwlevel_data
        if gwlevel_data is None:
            raise ValueError("必须提供 gwlevel_data 参数")

        for day, month, gwlevel in gwlevel_data:
            lines.append(f" {day:<6} {month:<6} {gwlevel:<7}   ")
        lines.append("* End of table")

        lines.append("*")
        lines.append("*")

        # SWOPT2 - 底部通量选项 - 使用传入的参数
        lines.append(f"  SWOPT2 = {self.swopt2_value} ! Switch, use regional bottom flux [Y=1, N=0]")
        lines.append("*")

        # 即使SWOPT2=0，也要包含完整的结构（按示例文件格式）
        lines.append("* If SWOPT2 = 1 specify whether a sine or a table are used to prescribe the flux:")
        sw2_value = self.sw2 if self.sw2 is not None else 2
        lines.append(f"  SWC2   = {sw2_value} ! Sine function = 1, table = 2")
        lines.append("*")

        lines.append("* In case of a sine function, specify:")
        c2ave_value = self.sinave if self.sinave is not None else ""
        c2amp_value = self.sinamp if self.sinamp is not None else ""
        c2max_value = self.sinmax if self.sinmax is not None else ""
        lines.append(f"  C2AVE = {c2ave_value} ! Average value of bottom flux [-10..10 cm/d, R, + = upwards]")
        lines.append(f"  C2AMP = {c2amp_value} ! Amplitude of bottom flux sine function [-10..10 cm/d, R]")
        lines.append(f"  C2MAX = {c2max_value} ! Daynumber with maximum bottom flux, [1..366 d, I]")
        lines.append("*")
        lines.append("* Table - specify date [day month] and bottom flux [-10..10 cm/d, R,+ = upwards]")
        lines.append("* d2 m2  QBottom        (maximum 366 records)")
        lines.append("* End of table")

        lines.append("*")
        lines.append("*")

        # SWOPT3 - 深层含水层选项 - 使用外部配置参数
        swopt3_val = self.swopt3_value
        lines.append(f"  SWOPT3 = {swopt3_val} ! Switch, calculate bottom flux from deep aquifer [Y=1, N=0]")
        lines.append("*")

        # SWOPT3参数 - 根据SWOPT3设置决定是否输出参数值
        swopt3_val = self.swopt3_value

        if swopt3_val == 1:
            # SWOPT3=1时，输出具体参数值
            shape_val = self.shape
            hdrain_val = self.hdrain
            rimlay_val = self.rimlay
            aqave_val = self.aqave
            aqamp_val = self.aqamp
            aqtamx_val = self.aqtamx
            aqper_val = self.aqper

            lines.append(f"  SHAPE  = {shape_val:<8.2f} ! Shape factor to derive average groundwater level, [0..1 -, R]")
            lines.append(f"  HDRAIN = {hdrain_val:<8.1f} ! Mean drain base to correct for average groundwater level, [-1.E4..0 cm, R]")
            lines.append(f"  RIMLAY = {rimlay_val:<8.1f} ! Vertical resistance of acquitard, [0..10000 d, R]")
            lines.append(f"  AQAVE  = {aqave_val:<8.1f} ! Average hydraulic head difference, [-1000..1000 cm, R]")
            lines.append(f"  AQAMP  = {aqamp_val:<8.1f} ! Amplitude of hydraulic head sine wave [0..1000 cm, R]")
            lines.append(f"  AQTAMX = {aqtamx_val:<8} ! first daynumber (Jan 1 = 1) with max. hydraulic head, [1..366 d, I]")
            lines.append(f"  AQPER  = {aqper_val:<8} ! Period of hydraulic head sine wave, [1..366 d, I]")
        else:
            # SWOPT3=0时，输出空值（严格按照标准案例格式）
            lines.append("  SHAPE  =  ! Shape factor to derive average groundwater level, [0..1 -, R]")
            lines.append("  HDRAIN =  ! Mean drain base to correct for average groundwater level, [-1.E4..0 cm, R]")
            lines.append("  RIMLAY =  ! Vertical resistance of acquitard, [0..10000 d, R]")
            lines.append("  AQAVE  =  ! Average hydraulic head difference, [-1000..1000 cm, R]")
            lines.append("  AQAMP  =  ! Amplitude of hydraulic head sine wave [0..1000 cm, R]")
            lines.append("  AQTAMX =  ! first daynumber (Jan 1 = 1) with max. hydraulic head, [1..366 d, I]")
            lines.append("  AQPER  =  ! Period of hydraulic head sine wave, [1..366 d, I]")
        
        lines.append("*")
        lines.append("*")
        
        # SWOPT4 - 地下水位函数选项 - 使用外部配置参数
        swopt4_val = self.swopt4_value
        lines.append(f"  SWOPT4 = {swopt4_val} ! Switch, calc. bottom flux as function of groundw. level, [Y=1, N=0]")
        lines.append("*")

        # SWOPT4参数 - 根据SWOPT4设置决定是否输出参数值
        lines.append("* If SWOPT4 = 1 specify of q = A exp(Bh) relation:")
        if swopt4_val == 1:
            # SWOPT4=1时，输出具体参数值
            cofqha_val = self.cofqha
            cofqhb_val = self.cofqhb
            lines.append(f"  COFQHA = {cofqha_val:<8.2f} ! coefficient A, [-100..100 cm/d, R]")
            lines.append(f"  COFQHB = {cofqhb_val:<8.4f} ! coefficient B, [-1..1 /cm, R]")
        else:
            # SWOPT4=0时，输出空值（严格按照标准案例格式）
            lines.append("  COFQHA =  ! coefficient A, [-100..100 cm/d, R]")
            lines.append("  COFQHB =  ! coefficient B, [-1..1 /cm, R]")

        lines.append("*")
        lines.append("*")

        # SWOPT5 - 底部隔室压头选项 - 使用外部配置参数
        swopt5_val = self.swopt5_value
        lines.append(f"  SWOPT5 = {swopt5_val} ! Switch, use pressure head of bottom compartment, [Y=1, N=0]")
        lines.append("*")

        # SWOPT5=1时，包含底部隔室压头数据（按示例文件格式）
        lines.append("* If SWOPT5 = 1, specify date [day month] and bottom compartment pressure")
        lines.append("*               head [-1.E10..1.E5 cm, negative if unsaturated]:")
        lines.append("* d5 m5  HGIVEN       (maximum 366 records)")

        # 底部隔室压头数据 - 使用外部配置数据
        hgiven_data = self.hbot5_data
        if hgiven_data is None:
            raise ValueError("必须提供 hbot5_data 参数")

        for day, month, hgiven in hgiven_data:
            lines.append(f" {day:<6} {month:<6} {hgiven:<7}   ")
        lines.append("* End of table")

        lines.append("*")
        lines.append("*")

        # SWOPT6 - 底部通量为零选项 - 使用外部配置参数
        swopt6_val = self.swopt6_value
        lines.append(f"  SWOPT6 = {swopt6_val} ! Switch, bottom flux equals zero [Y=1, N=0]")
        lines.append("*")
        lines.append("*")

        # SWOPT7 - 土壤剖面自由排水选项 - 使用外部配置参数
        swopt7_val = self.swopt7_value
        lines.append(f"  SWOPT7 = {swopt7_val} ! Switch, free drainage of soil profile [Y=1, N=0]")
        lines.append("*")
        lines.append("*")

        # SWOPT8 - 土壤-空气界面自由流出选项 - 使用外部配置参数
        swopt8_val = self.swopt8_value
        lines.append(f"  SWOPT8 = {swopt8_val} ! Switch, free outflow at soil-air interface [Y=1, N=0]")
        lines.append("*")
        
        lines.append("*" * 80)
        lines.append("")
        lines.append("*" * 80)
        lines.append("* Section 2  地下水浓度变化条件下的 (cxu)")
        swvgwc_val = self.swvgwc
        lines.append(f"  SWVGWC = {swvgwc_val} !   =1, using variable gw concentration; =0, constant gw concentration.")
        lines.append("* d1 m1  GWCTAB (g/L)        (maximum 366 records)")

        # 地下水浓度数据 - 使用外部配置数据
        gwc_data_tuples = self.gwc_data
        if gwc_data_tuples is None:
            raise ValueError("必须提供 gwc_data 参数")

        # 格式化地下水浓度数据
        for day, month, conc in gwc_data_tuples:
            lines.append(f" {day:<6} {month:<6} {conc:<7.2f}   ")
        lines.append("* End of table")

        lines.append("*" * 80)
        lines.append("")
        lines.append("*" * 80)
        lines.append("* Section 3   choose bottom boundary condition for heat simulation")
        lines.append("*")
        swhopt1_val = self.swhopt1
        lines.append(f"  SWHOPT1 = {swhopt1_val} !   =1, Switch, bottom flux equals zero [Y=1, N=0]")
        lines.append("*")
        lines.append("*")
        swhopt2_val = self.swhopt2
        lines.append(f"  SWHOPT2 = {swhopt2_val} !   =1, Switch, use Temp. of bottom compartment, [Y=1, N=0]")
        lines.append("*")
        lines.append("* If SWHOPT2 = 1, specify date [day month] and bottom compartment Temp")
        lines.append("*                 [-50..50 C]:")
        lines.append("* d6 m6  TGIVEN       (maximum 366 records)")
        lines.append("* End of table ")

        lines.append("*" * 80)
        lines.append("")
        lines.append("*" * 80)
        lines.append("* Section 4  using variable heat conductivity of soil surface material")
        lines.append("*")
        lines.append(f"  SWVHCS = {self.swvhcs} ! Switch, using variable heat conductivity of soil surface material (HCONSM); =0, using constant")
        lines.append("* HCONSM value which has been specifed in *.HEA file.")
        lines.append("*")
        lines.append("* If SWVHCS = 1, then specify heat conductivity of soil surface material")
        lines.append("* [0..20000 J/cm/K/d, R] ")
        lines.append("* d1 m1  HCSTAB        (maximum 366 records)")
        lines.append("* End of table")

        lines.append("*" * 80)
        lines.append("")
        lines.append("*" * 80)
        lines.append("* Section 5 adjust hydrological properties for surface soil")
        lines.append("*")
        swhpadj_val = self.swhpadj
        lines.append(f"  SWHPadj = {swhpadj_val} ! Switch, if adjust hydrological properties for surface soil [Y=1, N=0]")
        lines.append("* ")

        # 根据SWHPadj设置决定是否输出DEPadj参数值
        if swhpadj_val == 1:
            # SWHPadj=1时，输出具体参数值
            depadj_val = self.depadj
            lines.append(f"  DEPadj = {depadj_val} ! depth of surface soil layer to be adjusted , [-20000 - 0 cm, R]")
            lines.append("* If SWHPadj = 1, specify depth of surface soil [0..100 cm]:")
            
        else:
            # SWHPadj=0时
            lines.append("  DEPadj =  ! depth of surface soil layer to be adjusted , [-20000 - 0 cm, R]")

        lines.append("* If SWHPadj = 1, specify adjust coefficents [0..3.0, R] at user-specified date, maximum 20 records:")
        lines.append("* dd mm  coethe (-)  coeks (-)        (maximum 20  records)")
        lines.append("* End of table ")
        lines.append("* End of file " + "*" * 70)
        
        return "\n".join(lines)


    def model_string(self, **kwargs) -> str:
        """覆盖 model_string 方法以处理 swbbcfile 属性。

        此方法在最终序列化步骤中调用，此时每个部分都转换为字符串表示形式。
        因此，根据 swbbcfile 属性，此函数将返回：

            - 完整的节字符串表示形式，例如当所有边界条件都包含在 .swp 文件中时，或者；
            - 它将只包含 swbbcfile 和 bbcfil（定义其他参数时文件的名称）。
                在这种情况下，其他参数将使用 write_bbc 方法写入单独的 .bbc 文件。
        """
        if self.swbbcfile == 1:
            return super().model_string(include={"swbbcfile", "bbcfil"}, **kwargs)
        else:
            return super().model_string()

    def write_bbc(self, path: _Path):
        """将底部边界条件写入 .bbc 文件。

        此方法仅在 swbbcfile 属性设置为 1 时可用。
        将整个节设置（除了在 .swp 文件中定义的 swbbcfile 和 bbcfil）写入单独的 .bbc 文件。

        参数：
            path (Path): .bbc 文件将保存到的目录路径。
        """
        if self.swbbcfile != 1:
            msg = "Bottom boundary conditions are not set to be written to a .bbc file."
            raise ValueError(msg)

        self.save_file(string=self.bbc(), fname=self.bbcfil, path=path)
