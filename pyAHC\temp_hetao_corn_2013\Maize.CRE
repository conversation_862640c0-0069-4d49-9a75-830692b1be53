*
* Filename: Maize.CRE
* Contents: AHC v2.0 - Data for EPIC crop growth model
***********************************************************************************************
* Section 1: Crop factor or crop height
*
  SWCF = 2 ! choice between crop factor [=1] or crop height [=2], dev. is only related to Temp.
*
* If SWCF = 1, list crop factor [0.5..1.5, R],   as function of dev. stage [0..1 -,R]:
* If SWCF = 2, using crop height which is calculated by AHC program automatically, 
*              but need specify the first row for initial height, e.g. 0.00  1.0
* If SWCF = 3, list crop height [0..1000 cm, R], as function of dev. stage [0..1 -,R]:
*
* If SWCF = 1, list crop factor  [0.5..1.5, R],   as function of dev. stage [0..2 -,R]:
*   DVS    CF[-] (maximum 36 records)
  CFTB = 
   0.0        1.2       
   1.0        1.2       
* End of Table
*
* If SWCF = 2, specify initial crop height  
  CFINI = 0.0 ! initial crop height, [0..1000cm, R]
*
* If SWCF = 3, list crop height [0..1000 cm, R], as function of dev. stage [0..2 -,R]:
*   DVS    CH [cm]  (maximum 36 records)
  CHTB = 
   0.0        1.0       
   0.14       30.0      
   0.29       60.0      
   0.38       90.1      
   0.46       160.8     
   0.52       180.0     
   0.6        200.0     
   0.73       200.0     
   0.86       200.0     
   1.0        200.0     
* End of Table
***********************************************************************************************

***********************************************************************************************
* Section 2: surface resistance parameters
*
  SWRSC = 1 ! choice between RSC [1=constant] or [2=variable as DVS] 
* If SWRSC = 1, only specify RSC value and use this RSC as constant value in whole simulation.
  RSC = 70.0 ! Minimum canopy resistance [0..1000 s/m, R]  
* If SWRSC = 2, using varaible RSC as DVS specified as follows:  
  RSCTB =   
   0.0        80.0      
   0.50       80.0      
   0.51       80.0      
   0.80       80.0      
   0.81       150.0     
   1.0        150.0     
* Considering the effect of VPD on RSC
  frgmax = 0.75 ! fraction of maximum stomatal conductance that is achieved at the vapor 
*                       pressure deficit defined by VPDFR [0..1 -, R] frgmax=1.0时不考虑vpd 
  vpdfr  = 4.00 ! vapor pressure deficit at which FRGMAX is valid [0..10 kpa, R]
***********************************************************************************************

***********************************************************************************************
* Section 3: root water uptake compensation
*
  swcompen = 0 ! Switch, root water uptake compensation [Y=1, N=0]
* If swcompen = 1, Specify omgs value:  可跳过
  omgs = 0.8 ! Omega_s, tolerance value for water uptake compensation (-)[0-1, R] 
***********************************************************************************************

***********************************************************************************************
* Section 4: root water uptake: water stress
*
  SWAQUC =0! Choise, if crop is aquatic or semi-aquatic crop (e.g. rice or taro) [Y=1, N=0]
*
  SWROOTW = 1 ! Choise, choose different models for calc. root water uptake 
*                 = 1, use Feddes(1978) model for water stress
*                 = 2, use VG(1987) model for water stress       
* If SWROOTW = 1, list the below: Feddes model parameters for water stress
  HLIM1  = -1 ! No water extraction at higher pressure heads, [-100..100 cm, R]
  HLIM2U = -15.0 ! h below which optimum water extr. starts for top layer, [-1000..100 cm, R]
  HLIM2L = -15.0 ! h below which optimum water extr. starts for sub layer, [-1000..100 cm, R]
  HLIM3H = -400.0 ! h below which water uptake red. starts at high Tpot, [-10000..100 cm, R]
  HLIM3L = -600.0 ! h below which water uptake red. starts at low Tpot, [-10000..100 cm, R]
  HLIM4  = -8000.0 ! No water extraction at lower pressure heads, [-16000..100 cm, R] 
  ADCRH  = 0.5 ! Level of high atmospheric demand, [0..5 cm/d, R]
  ADCRL  = 0.1 ! Level of low atmospheric demand,  [0..5 cm/d, R]

* If SWROOTW = 2, list the below: VG model parameters for water stress
  P1     = 2.5  ! 
  H50    = -8000.0  ! 
************************************************************************************************

************************************************************************************************
* Section 5: root water uptake: salt stress 
*
  SWROOTS = 3! Choise, choose different models for calc. root water uptake 
*                 = 1, use maas (1990) model for salt stress
*                 = 2, use VG (1987)model for salt stress
*                 = 3, use Dirksen (1987) model for salt stress
* If SWROOTS=1, list maas(1990) model parameters for salt stress  
  ECMAX  = 3.5 ! ECsat level at which salt stress starts, [0..20 dS/m, R] 
  ECSLOP = 8.0 ! Decline of rootwater uptake above E_CMAX [0..40 %/dS/m, R]
*
* If SWROOTS = 2 or 3, list VG or Dirksen(1987) model parameters for salt stress
  H50C   = -6000.0 ! Value of the osmotic head at which the root water uptake is reduced by 50%
  CI     = -537.0 ! Coefficient for converting solution concentration (g/L) to osmotic head (cm)
* If SWROOTS = 2, list other VG (1987) model parameters
  P2V    = 3.0 ! Exponent in S-shaped (VG) root water uptake salinity stress response function
* If SWROOTS = 3, list other Dirksen(1987) model parameters
  P2D    = 1.86 ! Exponent in Dirksen root water uptake salinity stress response function
  hcritx = -2000.0 ! Threshold value of osmotic head above which no salinity stress happens
***********************************************************************************************

***********************************************************************************************
* Section 6: Interception of rainfall  and effects of withered canopy cover on Ep
*
  COFAB  = 0.25 ! Interception coefficient Von Hoyningen-Hune and Braden, [0..1 cm, R]
  FWCC   = 0.6     ! Coefficient for sheltering effect of withered canopy cover on Ep [0..1.0, R]
***********************************************************************************************

***********************************************************************************************
* Section 7: Root density distribution and root growth 
*
* List relative root density [0..1 -, R], as function of rel. rooting depth [0..1 -, R]:
*    RD     RDC   (maximum 11 records)
  RDCTB = 
   0.0        0.8       
   0.15       0.95      
   0.25       1.05      
   0.35       1.1       
   0.5        1.0       
   0.6        0.7       
   0.65       0.38      
   0.7        0.24      
   0.8        0.15      
   0.9        0.1       
   1.0        0.05      
* End of table
*
* List fraction of total dry matter increase partitioned to the roots [kg/kg, R]
* as function of development stage [0..1 -, R]
*          DVS     FR    (maximum 15 records)
  FRTB = 
   0.0        0.8       
   0.2        0.6       
   0.5        0.3       
   0.7        0.1       
   1.0        0.02      
* End of table
*
  SWRTCR = 0 ! If calculating root CO2 flux from respiration [Y=1, N=0]
* If SWRTCR = 1, specify below values:
  RATRES = 26.18! the average rate of respiration (mg CO2-C/g dry matter/d) [0..100.0 -, R]
* List temperature sensitivity parameter of root respiration as function of DVS [0.0..1.0 -, R]
*    DVS      Q12P   (maximum 15 records)
  Q12PTB = 
   0.0        5.0       
   0.5        2.6       
   1.0        1.6       
* End of table
***********************************************************************************************

***********************************************************************************************
* Section 8: light extinction 
*
* List extinction coefficient for global solar radiation as function of DVS [0.3..1.0 -, R]
*    DVS      KGSR   (maximum 15 records)
  KGSRTB = 
   0.0        0.62      
   1.0        0.62      
* End of table
* List extinction coefficient for photosynthetically-active radiation (PAR) as function of DVS [0.4..1.2 -, R]
*    DVS      KPAR   (maximum 15 records)
  KPARTB = 
   0.0        0.65      
   1.0        0.65      
* End of table
***********************************************************************************************

***********************************************************************************************
* Section 9： read crop parameters in EPIC crop growth module
*
 TBA =9.0! Base temperature for crop grwoth, [0..30.0 -, R]
 TOPT =25.0! Optimal temperature for crop growth, [0..60.0 -, R]
 RUEP =47.0! Potential plant radiation-use efficiency at 330ppm CO2, [0..120.0 -, R]
 DLAP1 =15.03! Combination parameters controlling the first point on optimal LAI development curve, [0..100&0..100, R], 
*              Number before decimal is % of growing season, and number after decimal is % of maximum potential LAI
 DLAP2=60.95 ! Combination parameters controlling the first point on optimal LAI development curve, [0..100&0..100, R], 
*              Number before decimal is % of growing season, and number after decimal is % of maximum potential LAI
 FDLAI =0.8 ! Fraction of growing season when LAI delinces, [0..1.0 -, R]
 RLAD =0.8  ! LAI decline rate parameter (1.0 is linear; >1,accelerates; <1 retards decline rate), [0..10.0 -, R]
 CHmax =265.0 ! Maximum crop/canopy height, [0..5000 cm -, R]
 Lmax =5.0 ! Potential maximum Leaf area index, [0..1.0 -, R]
 RDmax =90.0! Maximum root depth, [0..5000 cm -, R]
 bn1 =0.044  ! Optimal nitrogen demand for seedling period, [0..1 kgN/kg biomass, R]
 bn2 =0.015  ! Optimal nitrogen demand for mid-growth period, [0..1 kgN/kg biomass, R]
 bn3 =0.010  ! Optimal nitrogen demand for maturity period, [0..1 kgN/kg biomass, R]
 HIpot =0.52! Potential harvest index at crop maturity, [0..1.0 -, R]
 HImin =0.40! Lower limit of harvest index under water-stress stress, [0..1.0 -, R]
 PHU =1900  ! Total potential heat units required for crop maturation, [0..3000 -, R]
 DRUEVP =8.0 ! Decline of RUE per unit increase in VPD [0..120 (kg/ha)/(MJ/m2*kPa), R]
***********************************************************************************************

***********************************************************************************************
* Section 10: active root nitrogen uptake, only used for nitrogen simulation M-M equation
*
  SWACTN = 0 ! Switch, active root nitrogen uptake [Y=1, N=0]
* If SWACTN = 1, specify below values:
  PWRTS  = 30000 ! Number of plant per hectare (plant/ha), [0..1.0E5 -, I]
  CNUP1  = 5.5 ! Maximum nitrogen uptake rate (g/plant/day), [0..10.0 -, R]
  CNUP2  = 1.5 ! Nitrogen uptake efficiency coefficient (ppm, ug N/cm3 water) [0..10.0 -, R]
***********************************************************************************************

***********************************************************************************************
* Section 11: maximum ponding depth control during crop growth period
*
  SWPDA =  0 ! Switch, if adjust maximum depth of surface ponding [Y=1, N=0]
* If SWPONDA = 1, specify the adjusted pondmxc [0..100 cm,R] as function of DVS [0..2 -,R]: 
*  DVS  PDMXC    (maximum 36 records)  
 PDMXCTB =  
   0.0        5.0       
   0.2        5.0       
   0.2        3.0       
   0.4        3.0       
   0.4        2.0       
   0.6        2.0       
   0.6        4.0       
   0.8        4.0       
   0.8        2.0       
   1.0        2.0       
* End of table
***********************************************************************************************

***********************************************************************************************
* Section 12: the effects of CO2 level on leaf conductance(i.e. RSC) and max LAI
*
  SWCO2 = 1 ! Switch, effects of CO2 level on crops[Y=1, N=0]
* If SWACO2 = 1, specify below three parameters:
  CO2    = 330.0! CO2 concentration [0..1000 ppmv, R] C_O2=330C_O2
  PDLC   = 0.40 ! Percentage decrease in leaf conductance at 660 ppm [0..0.5 -,R]
  PILM   = 0.40 ! Percentage increase in maximum LAI at 660 ppm [0..0.5 -,R]
  RUECP2 = 660.50 ! Combination parameters controlling the second point on C_O2 effect curve on RUE, [300..1500 & 0..120 -, R],
*                       Number before decimal is a value of C_O2 concentration higher than ambient (e.g. 450 or 660 ppm or uL/L) 
*                       above the ambient, and number after decimal is the corrsponding RUE value.
***********************************************************************************************

***********************************************************************************************
* Section 13: Initial values 
*
  HUIINI = 0.089 ! initial HUI for the transplanted crops [0.0..1.0, R]
  LAIINI = 0.03 ! Initial leaf area index [0..10 -, R]
  RDINI  = 20.0 ! Initial root depth, [0..1000 cm, R]
  BIOINI = 9.7 ! Initial total crop dry biomass [0.0..10000 kg/ha, R]
***********************************************************************************************
* End of file
**********************************
* End of file