* Project: hetao
* Filename: hetao.SWP
* Contents: AHC 2.0 - Soil water and profile data
***********************************************************************************************
* Case: Water and solute transport in the Huinong area.
***********************************************************************************************

***********************************************************************************************
* Section 1: Surface runoff  * Section 1: Ponding
*
  SWSROM = 1 ! Choi<PERSON>, method for calculation of surface runoff
*              1 = use the surface reservoir boundary condition
*              2 = use the SCS curve number method 
*
  PONDMX = 20.0 ! Maximum thickness of ponding water layer [0..1000 cm, R]
  RSRO = 20.0 ! Drainage resistance for surface runoff [0.001..1.0 d, R]
  ROEXP = 1.0 ! Exponent in drainage equation of surface runoff [0.1..10.0 -, R]
*
* If SWSROM = 2,then specify
  CNV = 67.0 ! Curve number value [20...100,R]
  DEPX =  ! Maximum depth for the adjustment of C-N value [0...200 cm, R]
****************** *****************************************************************************

* Section 2: soil evaporation
  SWCFBS = 0 ! Use CFBS to calculate ES0 from ET0 [Y=1, N=0]
* If SWCFBS = 1 specify CFBS:
  CFBS   = 1.4 ! Conversion factor [0.5..1.5, R]
*
  SWREDU = 3 ! Switch, method for reduction of soil evaporation
*              0 = reduction to maximum Darcy flux
*              1 = reduction to maximum Darcy flux and to maximum Black (1969),
*              2 = reduction to maximum Darcy flux and to maximum Bo/Str. (1986),
*              3 = reduction using FAO method [good for shallow water table
*If SWREDU 1 or 2 then specify:
  COFRED = 0.6 ! soil evaporation coefficient of Black, [0..1 cm/d1/2, R],
*                                 or Boesten/Stroosnijder, [0..1 cm1/2, R]
 RSIGNI = 0.50 ! Minimum rainfall to reset models Black and Bo/Str., [0..1 cm/d, R]
*If SWREDU=3 then specify:
 FK = 4.0 ! decline factor [1...8, R]
 DZSEL = 15 ! Thickness for surface evaporation layer [0...40 cm,R]
 hFCSL= -100 ! h at field capacity for surface evaporation layer [-330...-100 cm, R]
 hWPSL = -8000.0 ! h at wilting point for surface evaporation layer  [-12000...-6000 cm, R]
***********************************************************************************************

***********************************************************************************************
* Section 3: Time discretization of Richards equation
*
  DTMIN  = 1.00E-04 ! Minimum timestep [1.0E-8..0.1 d, R]
  DTMAX  = 0.2 ! Maximum timestep [0.01..0.5 d, R]
  THETOL = 0.009 ! Max. dif. water content between iterations, [1.E-5..0.01 cm3/cm3, R]
  SWMHC= 1! Switch, calculate the mean hydraulic conductivity between two nodes [1..6,I]
*                     1 = Arithmetric mean
*                     2 = Harmonic mean
*                     3 = Geometric mean/Logarithmic mean
*                     4 = Weighted arithmetric mean
*                     5 = Weighted harmonic mean
*                     6 = Weighted geometric mean
***********************************************************************************************

***********************************************************************************************
* Section 4: Spatial discretization
*
  NUMLAY = 4 ! Number of soil layers,[1..10, I]
  NUMNOD = 300 ! Number of soil compartments, [1..500, I] 
*
* List compartment number at bottom of each soil layer (max 5), [1..40, I]
 BOTCOM = 
 40    130    240    300    
*
* List thickness of each soil compartment (max 40), [0..500 cm, R]:
  DZ = 
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
 1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    1.0    
* End of table
***********************************************************************************************
 
***********************************************************************************************
* Section 5: Soil hydraulic fucntions and maximum rooting depth
* Specify for each soil layer the soil texture (g/g mineral parts) and
* the organic matter content (g/g dry soil), Bulk density (g/cm3)
*   PSand    PSilt    PClay   OrgMat  Bdens
 0.3037    0.5751    0.1211    0.0163    1.40      
 0.2949    0.5958    0.1092    0.0163    1.40      
 0.0384    0.7514    0.2102    0.0163    1.40      
 0.34      0.5832    0.0768    0.0163    1.40      
* End of table
*
  RDS    = 90.0 ! Maximum rooting depth allowed by the soil [0..1000 cm, R]
***********************************************************************************************

***********************************************************************************************
* Section 6: Hysteresis of soil water retention function
*
  SWHYST = 0 ! Switch, hysteresis:
*              0 = no hysteresis
*              1 = hysteresis, initial condition wetting
*              2 = hysteresis, initial condition drying
*
* If SWHYST 1 or 2 then specify:
  TAU    =  ! Minimum press. head difference to change wetting-drying, [0..1 cm, R]
***********************************************************************************************

***********************************************************************************************
* Section 7: similar media scaling of soil hydraulic functions
*
 SWSCAL = 0 ! Switch, similar media scaling [Y=1, N=0]
*
* If SWSCAL = 1 specify:
  NSCALE = 1 ! Number of runs [1..30, I]
  ISCLAY = 1 ! Soil layer number to which scaling is applied [1..5,I] 
*
* List scaling factors of each run (max. = 30), [0..100, R]
  FSCALE = 
 
***********************************************************************************************

***********************************************************************************************
* Section 8: Preferential flow due to soil volumes with immobile water
*
  SWMOBI = 0 ! Switch, preferential flow due to immobile water [Y=1, N=0]
*
* If SWMOBI = 1, specify mobile fraction as function of log -h for each soil layer: 
* PF1    first datapoint;  log -h [0..5, R]
* FM1    first datapoint;  mobile fraction (1.0 = totally mobile), [0..1 -, R]
* PF2    second datapoint; log h [0..5,  R]
* FM2    second datapoint; mobile fraction (1.0 = totally mobile), [0..1 -, R]
* Also specify volumetric water content in immobile soil volume (THETIM), [0..0.3 -, R]
*   PF1    FM1    PF2    FM2   THETIM 
 0.0     0.4     3.0     0.4     0.02    
 0.0     1.0     3.0     1.0     0.02    
 0.0     1.0     3.0     1.0     0.02    
 0.0     1.0     3.0     1.0     0.02    
 0.0     1.0     3.0     1.0     0.02    
* End of table
***********************************************************************************************

***********************************************************************************************
* Section 9: Preferential flow due to soil cracks
*
SWCRACK = 0 ! Switch, soil cracks [Y=1, N=0]
*
* If SWCRACK = 1, specify: 
  SHRINA  = 1.0 ! Void ratio at zero water content [-]
  MOISR1  = 1.2 ! Moisture ratio at trans. residual ---> normal shrink [0..5 cm3/cm3,R]
  MOISRD  = 0.2 ! Structural shrinkage [0..1 cm3/cm3, R]
  ZNCRACK = -10.0 ! Depth at which crack area of soil surface is calculated [-100..0 cm, R]
  GEOMF   = 5 ! Geometry factor (3 = isotropic shrinkage), [0..100, R]
  DIAMPOL = 5 ! Diameter soil matrix polygon [0..100 cm, R]
  RAPCOEF = 200 ! Rate coef. bypass flow from cracks to surface water [0..10000 /d, R]
  DIFDES  = 160 ! Effective lateral solute diffusion coefficient [0..10000 /d, R]
*
* If SWCRACK = 1, specify also crit. water content of each soil layer (max. 5), [0..1, R]
* if actual water content becomes less than critical water content, cracks are formed
  THETCR = 
 0.1    0.12   0.1    0.1    0.08   
***********************************************************************************************
 
***********************************************************************************************
* Section 10: vertical distribution drainage flux in saturated part soil column
*
  SWDIVD = 1 ! Switch, apply vertical distribution [Y=1, N=0]
*
* If SWDIVD = 1, specify anisotropy factor (vertical/horizontal saturated hydraulic
*                conductivity) for each soil layer (max. 5), [0..1000 -, R]:
  COFANI = 
 0.2    0.2    0.2    0.2                                              
***********************************************************************************************

***********************************************************************************************
* Section 11: Initial moisture condition
*
  SWINCO = 0 ! Switch, type of initial moisture condition:
*              0 = soil water content (theta) T_HETAI
*              1 = pressure head of each compartment is input, H_I
*              2 = pressure head of each compartment is in hydrostatic equilibrium with
*                  initial groundwater table,
* If SWINCO = 0, specify initial soil water content (max 500), [0-1.0 cm3/cm3, R] 
* If SWINCO = 1, specify initial h (max 500), [-1.E10..200 cm, R] 
 THETAI = 
 0.26      0.26      0.26      0.26      0.26      0.26      0.26      0.26      0.26      0.26      
 0.32      0.32      0.32      0.32      0.32      0.32      0.32      0.32      0.32      0.32      
 0.30      0.30      0.30      0.30      0.30      0.30      0.30      0.30      0.30      0.30      
 0.30      0.30      0.30      0.30      0.30      0.30      0.30      0.30      0.30      0.30      
 0.34      0.34      0.34      0.34      0.34      0.34      0.34      0.34      0.34      0.34      
 0.34      0.34      0.34      0.34      0.34      0.34      0.34      0.34      0.34      0.34      
 0.39      0.39      0.39      0.39      0.39      0.39      0.39      0.39      0.39      0.39      
 0.39      0.39      0.39      0.39      0.39      0.39      0.39      0.39      0.39      0.39      
 0.38      0.38      0.38      0.38      0.38      0.38      0.38      0.38      0.38      0.38      
 0.38      0.38      0.38      0.38      0.38      0.38      0.38      0.38      0.38      0.38      
 0.38      0.38      0.39      0.39      0.39      0.39      0.40      0.40      0.40      0.40      
 0.40      0.41      0.41      0.41      0.41      0.41      0.42      0.42      0.42      0.42      
 0.43      0.43      0.43      0.43      0.43      0.44      0.44      0.44      0.44      0.44      
 0.45      0.45      0.45      0.45      0.46      0.46      0.46      0.46      0.46      0.46      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      0.47      
 0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      
 0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      
 0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      
 0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      
 0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      
 0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      0.45      
*
* If SWINCO = 2, specify:
  GWLI   = -300 ! Initial groundwater level, [-5000..100 cm, R]
  QSTI   =  ! water flux under steady-state condition at initial state [-3..3 cm/d, R]
*
***********************************************************************************************

***********************************************************************************************
* Section 12: Ice melting process in early period
*
  swice   = 0 ! Switch, ice melting process [Y=1, N=0]
* If swice = 1, specify:    y=a*x^b+c
  daymels = 73.0 ! start day of thaw from Jan 1st=0 [d]
  pa      = 0.035 ! coefficient a [-]
  pb      = 2.0 ! coefficient b [-]
  pc      = 10.0 ! coefficient c for initial unfrozen depth [-]
  maxdp   = 80.0 ! melting depth between up and below layer [cm]
  gwlmelt = -117.8 ! gwl at initial of fully melting day [d]
*
***********************************************************************************************
 
***********************************************************************************************
* Section 13: Snow and frost
*
  SWSNOW  = 0 ! Switch, calculate snow accumulation and melt. [Y=1, N=0]
*
* If SWSNOW = 1, then specify initial snow water equivalent and snowmelt factor
  SNOWINCO= 0.0 ! the initial SWE (Snow Water Equivalent), [0.0...1000.0 cm, R]
  SNOWCOEF= 0.3 ! calibration factor for snowmelt, [0.0...10.0 -, R]
*
  SWFROST= 0! Switch, in case of frost: stop soil water flow [0..2,I]
*                     0 = None forst treatment
*                     1 = Reduce water flow in case of frost
*                     2 = Simulate soil freezing and thawing
*
* End of file *********************************************************************************