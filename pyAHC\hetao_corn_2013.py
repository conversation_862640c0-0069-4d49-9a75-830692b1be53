#!/usr/bin/env python3
"""AHC-V201.exe 执行案例 - 使用新的验证框架

这个案例文件展示了如何使用 pyAHC 库生成所有必需的输入文件，
然后运行 AHC-V201.exe 进行水文模拟。

主要功能：
1. 创建完整的模型配置（支持新的验证框架和外部化配置）
2. 生成所有输入文件
3. 运行 AHC-V201.exe
4. 检查输出结果
5. 演示新验证框架的使用方法

新增功能：
- 使用外部化配置创建组件
- 验证框架集成和错误处理
- 内置验证功能（无需生成报告文件）
"""

import subprocess
from pathlib import Path
import datetime

from pyahc.model.model import Model, ModelBuilder
from pyahc.components.ctr import CtrFile as _CtrFile
from pyahc.components.metadata import Metadata
from pyahc.components.simsettings import GeneralSettings
from pyahc.components.meteorology import Meteorology, MetFile
from pyahc.components.crop import Crop, CropFile
from pyahc.components.irrigation import FixedIrrigation
from pyahc.components.soilwater import SoilMoisture, SurfaceFlow, Evaporation, SoilProfile, SnowAndFrost
from pyahc.components.simsettings import RichardsSettings
from pyahc.components.drainage import Drainage, DraFile
from pyahc.components.boundary import BottomBoundary as _BottomBoundary
from pyahc.components.transport import HeatFlow, SoluteTransport
from pyahc.components.epic_crop import EpicCrop as _EpicCrop
from pyahc.components.observation import ObservationPoints as _ObservationPoints
from pyahc.core.validation_framework import ValidationManager as _ValidationManager

# 导入数据文件
from data.gwlevel_data import GWLEVEL_DATA
from data.hbot5_data import HBOT5_DATA
from data.gwc_data import GWC_DATA
from data.thetai_data import STANDARD_THETAI
from data.maize_default import MAIZE_DEFAULT_PARAMS
from data.meteorological_data import MeteorologicalDataManager



def update_meteorology_data(meteorology, meteo_manager, new_data_list=None, end_date=None):
    """更新气象组件的数据

    Args:
        meteorology: Meteorology对象
        meteo_manager: MeteorologicalDataManager对象
        new_data_list: 新的气象数据列表，格式为:
                      [(day, month, year, rad, tmin, tmax, hum, wind, rain, etref, wet), ...]
        end_date: 结束日期（可选）

    Returns:
        Meteorology: 更新后的Meteorology对象
    """

    # 添加新数据（如果提供）
    if new_data_list:
        for data in new_data_list:
            meteo_manager.add_daily_data(*data)
        print(f"✓ 已添加 {len(new_data_list)} 天新的气象数据")

    # 重新生成DataFrame
    if end_date:
        df = meteo_manager.to_dataframe(end_date=end_date)
    else:
        df = meteo_manager.to_dataframe()

    # 更新MetFile内容
    meteorology.metfile.content = df

    return meteorology


def create_model_components():
    """创建模型组件 - 配置即创建模式

    Returns:
        tuple: (Model对象, CtrFile对象, MeteorologicalDataManager对象)
    """

    # 共享基础参数
    start_date = datetime.date(2013, 5, 20)
    end_date = datetime.date(2013, 5, 30)
    project = "hetao"
    lat, alt = 45.75, 1039.3

    # ==================== 直接创建组件 ====================

    # === 核心控制组件 ===
    ctr_file = _CtrFile.from_config({
        'project': project,
        'ssrun': [start_date.day, start_date.month, start_date.year],
        'esrun': [end_date.day, end_date.month, end_date.year],
        'metfil': "linhe", 'lat': lat, 'alt': alt,
        'irgfil': "hetao", 'calfil': "YEAR13", 'drfil': "hetao",
        'bbcfil': "hetao", 'outfil': "rs0",
        'swodat': 0, 'swdra': 1, 'swsolu': 1, 'swstyp': 1,
        'swhea': 0, 'swvapt': 0, 'swvap': 1, 'swdrf': 0, 'swswb': 0,
        'balnode': 200, 'iclb': 0,
        'output_dates': [
            [8, 4, 2013], [15, 4, 2013], [27, 4, 2013], [2, 5, 2013], [3, 5, 2013],
            [5, 5, 2013], [8, 5, 2013], [9, 5, 2013], [11, 5, 2013], [13, 5, 2013],
            [14, 5, 2013], [16, 5, 2013], [18, 5, 2013], [20, 5, 2013], [22, 5, 2013],
            [24, 5, 2013], [25, 5, 2013], [27, 5, 2013], [30, 5, 2013], [2, 6, 2013],
            [4, 6, 2013], [5, 6, 2013], [6, 6, 2013], [8, 6, 2013], [10, 6, 2013],
            [13, 6, 2013], [15, 6, 2013], [18, 6, 2013], [20, 6, 2013], [22, 6, 2013],
            [26, 6, 2013], [28, 6, 2013], [1, 7, 2013], [3, 7, 2013], [5, 7, 2013],
            [6, 7, 2013], [9, 7, 2013], [12, 7, 2013]
        ]
    })

    bottomboundary = _BottomBoundary.from_config({
        # 基础开关参数
        'swbbcfile': 1,
        'bbcfil': "hetao",
        'swbotb': 5,

        # 深层含水层参数（明确设置，不依赖组件内部默认值）
        'shape': 1.0,
        'hdrain': -120.0,
        'rimlay': 500.0,
        'aqave': 0.0,
        'aqamp': 0.0,
        'aqtamx': 0,
        'aqper': 365,

        # 地下水位函数参数
        'cofqha': 0.0,
        'cofqhb': 0.0,
        'cofqhc': 0.0,

        # 其他开关参数
        'sw2': 2,
        'sw3': 1,
        'swqhbot': 1,

        # 正弦函数参数
        'sinave': 0.0,
        'sinamp': 0.0,
        'sinmax': 1,

        # 开关参数（明确设置所有值）
        'swopt1_value': 0,
        'swopt2_value': 0,
        'swopt3_value': 0,
        'swopt4_value': 0,
        'swopt5_value': 1,
        'swopt6_value': 0,
        'swopt7_value': 0,
        'swopt8_value': 0,

        # 地下水浓度开关
        'swvgwc': 1,

        # 热模拟参数
        'swhopt1': 0,
        'swhopt2': 1,
        'swvhcs': 0,

        # 表层土壤调整参数
        'swhpadj': 0,
        'depadj': 0,

        # 数据表
        'gwlevel_data': GWLEVEL_DATA,
        'hbot5_data': HBOT5_DATA,
        'gwc_data': GWC_DATA
    })

    # === 作物组件 ===
    epic_crop = _EpicCrop.from_config(MAIZE_DEFAULT_PARAMS)

    
    # === 作物管理 ===
    crop_file = CropFile(
        name="YEAR13", crop_name="Maize", crop_type=2, capfil="",
        sowing_day=2, sowing_month=5, end_day=21, end_month=9,
        start_sch_day=8, start_sch_month=10, swdorm=0,
        dormancy_start_day=29, dormancy_start_month=12, dormancy_start_year=2010,
        dormancy_end_day=25, dormancy_end_month=2, dormancy_end_year=2011,
        swmulch=1, mulch_start_day=4, mulch_start_month=5, mulch_start_year=2013,
        mulch_end_day=25, mulch_end_month=9, mulch_end_year=2013,
        fmat=1.0, mfrac=0.5
    )
    crop = Crop(cropfiles={"YEAR13.CAL": crop_file})

    obs_points = _ObservationPoints.from_config({
        'obsn': 6,
        'observation_depths': [(0, 10), (10, 20), (20, 40), (40, 60), (60, 80), (80, 100)],
        'repeat_count': 2,
        'obsn_format_template': ' {obsn}   {obsn}      #OBSN=Number of observed layer, {obsn} layer is recommond',
        'depth_format_template': ' {upper:<6} {lower:<6}      ',
        'min_depth_difference': 1,
        'max_depth': 1000
    })

    # === 基础组件 ===
    metadata = Metadata(
        author="Test User", institution="Test Institution",
        email="<EMAIL>", project_name="hetao_test", ahc_version="4.2.0"
    )

    generalsettings = GeneralSettings(
        tstart=start_date, tend=end_date,
        default_nprintday=1, default_swscre=1, default_swerror=0,
        default_swheader=1, default_swafo=0, default_swaun=0, default_swdiscrvert=0
    )

    # === 气象数据 ===
    # 创建动态气象数据管理器
    meteo_manager = MeteorologicalDataManager(station_name="linhe")
    df = meteo_manager.to_dataframe()
    metfile = MetFile(metfil="linhe", content=df)
    meteorology = Meteorology(metfile=metfile)

    data_count = meteo_manager.get_data_count()
    print(f"✓ 动态气象数据管理器创建完成")
    print(f"  - 基础数据: {data_count['base_data_count']} 天")
    print(f"  - 总计: {data_count['total_count']} 天")


    fixedirrigation = FixedIrrigation(
        swirgfil=1, irgfil="hetao",
        irrigation_events=[
            (27, 6, 113, 15.8),  # 6月27日，113mm，15.8°C
            (16, 7, 94, 24),     # 7月16日，94mm，24°C
            (7, 8, 108, 29)      # 8月7日，108mm，29°C
        ],
        irrigation_temperature=20.0, irrigation_wetfraction=1.0,
        irrigation_concentration=0.5, irrigation_method=1
    )

    # === 土壤水分组件 ===
    soilmoisture = SoilMoisture(swinco=0, thetai=STANDARD_THETAI)

    surfaceflow = SurfaceFlow(
        swsrom=1, swpondmx=0, pondmx=20.0, rsro=20.0,
        rsroexp=1.0, swrunon=0, cnv=67
    )

    evaporation = Evaporation(
        swcfbs=0, cfbs=1.4, swredu=3, cofredbl=0.6, rsigni=0.50,
        fk=4.0, dzsel=15, hfcsl=-100, hwpsl=-8000.0
    )

    soilprofile = SoilProfile(
        swsophy=0, swhyst=0, tau=0.2, swmacro=0, rds=90.0,
        swdivd=1, cofani=[0.2, 0.2, 0.2, 0.2]
    )

    snowandfrost = SnowAndFrost(
        swice=0, swsnow=0, swfrost=0, snowinco=0.0, snowcoef=0.3,
        daymels=73.0, pa=0.035, pb=2.0, pc=10.0, maxdp=80.0, gwlmelt=-117.8
    )

    # === 数值求解 ===
    richards = RichardsSettings(
        dtmin=1.00E-04, dtmax=0.2,
        gwlconv=100.0, critdevh1cp=0.01, critdevh2cp=0.1,
        critdevponddt=0.0001, maxit=30, maxbacktr=3
    )
    
    # === 排水组件 ===
    # 创建排水文件模板（保留作为模板，无论swdra值如何）
    dra_file = DraFile(
        lm1=None, lm2=80.0, shape=1.0, wetper=810.0, zbotdr=-120.0, entres=100.0,
        nrlevs=1, filename='hetao', default_basegw=-4500.0, default_khtop=580.0,
        default_lm2=80.0, default_wetper=810.0, default_zbotdr=-120.0, default_entres=100.0,
        drares=[None]*5, infres=[None]*5, swallo=[1]*5,
        l=[None]*5, zbotdr_level=[None]*5, swdtyp=[1]*5
    )

    # 根据ctr_file中的swdra设置来决定是否启用排水
    # 当swdra为0时，不生成hetao.DRB文件或调用排水功能
    ctr_swdra = ctr_file.swdra  # 从ctr_file配置中获取swdra值
    lateraldrainage = Drainage(
        swdra=ctr_swdra,
        drfil="hetao",
        drafile=dra_file if ctr_swdra > 0 else None
    )

    # === 传输组件 ===
    # 根据ctr_file中的swhea设置来决定是否启用热传输
    # 当swhea为0时，不生成hetao.HEA文件或调用热传输功能
    ctr_swhea = ctr_file.swhea  # 从ctr_file配置中获取swhea值
    heatflow = HeatFlow(
        swhea=ctr_swhea, swshf=2, tampli=10.0, tmean=15.0, ddamp=50.0,
        timref=90.0, swessm=0, hconsm=1.0, tempi=[12.0] * 300
    )

    solutetransport = SoluteTransport(
        numsat=1, swcalm=1, chrsun=1, swocn=0, swsolu=1, cpre=0.0, ddif=3.5,
        ldis=[20.0]*4, tscf=0.0, swsp=0, kf=[0.05]*4, frexp=1.0, cref=1.0, eta=[0.1]*4, swdc=0,
        decpot=[0.0]*4, decpots=[0.0]*4, decpot_c=[0.0]*5, decpots_c=[0.0]*5,
        gampar=0.2, rtheta=0.3, bexp=1.2, fdepth=[0.10]*5,
        chm_inf=[(30.0, 5.0, 7.8), (45.0, 5.0, 7.8), (70.0, 5.0, 7.8), (100.0, 5.0, 7.8), (300.0, 5.0, 7.8)],
        swpref=0, kmobil=[0.1], swbr=0, cdrain=2.0, swbotbc=1, cseep=1.0,
        daquif=290, poros=0.3, kfsat=10.3, decsat=1.2, cdraini=1.7
    )

    # ==================== 模型组装 ====================
    model = Model(
        metadata=metadata,
        generalsettings=generalsettings,
        meteorology=meteorology,
        crop=crop,
        fixedirrigation=fixedirrigation,
        soilmoisture=soilmoisture,
        surfaceflow=surfaceflow,
        evaporation=evaporation,
        soilprofile=soilprofile,
        snowandfrost=snowandfrost,
        richards=richards,
        lateraldrainage=lateraldrainage,
        bottomboundary=bottomboundary,
        heatflow=heatflow,
        solutetransport=solutetransport,
        epic_crop=epic_crop,
        obs_points=obs_points,
        ctr_file=ctr_file
    )

    # ==================== 验证处理 ====================
    _perform_validation(ctr_file, bottomboundary, model.epic_crop, model.obs_points)

    print("✓ 所有模型组件创建完成")
    return model, ctr_file, meteo_manager


def _perform_validation(ctr_file, bottomboundary, epic_crop, obs_points):
    """执行模型验证"""
    try:
        for component in [ctr_file, bottomboundary, epic_crop, obs_points]:
            if hasattr(component, 'enable_validation'):
                component.enable_validation()

        validation_manager = _ValidationManager()
        components = {
            'CtrFile': ctr_file, 'BottomBoundary': bottomboundary,
            'EpicCrop': epic_crop, 'ObservationPoints': obs_points
        }

        model_report = validation_manager.validate_model(components)

        if model_report['model_status'] == 'passed':
            print("✓ 所有组件验证通过")
        else:
            print("✗ 组件验证失败:")
            for component_name, report in model_report['component_reports'].items():
                if report['status'] == 'failed':
                    print(f"  - {component_name}: {report['errors']}")
            if model_report['cross_component_errors']:
                print(f"  - 组件间依赖错误: {model_report['cross_component_errors']}")
    except Exception as e:
        print(f"⚠ 验证过程中出现错误: {e}")


def generate_input_files(model, output_dir):
    """生成所有输入文件。

    Args:
        model: 模型对象（包含所有组件）
        output_dir: 输出目录
    """
    # 使用 ModelBuilder 生成所有文件（包括新集成的组件）
    builder = ModelBuilder(model, output_dir)

    # 1. 复制可执行文件
    builder.copy_executable()
    print("✓ ahc-v201.exe 复制完成")

    # 2. 生成所有输入文件（现在包括 EPIC 作物和观测点文件）
    builder.write_inputs()
    print("✓ 所有输入文件生成完成")
    

def run_ahc_executable(output_dir):
    """运行 AHC-V201.exe 可执行文件。

    Args:
        output_dir: 输出目录

    Returns:
        bool: 运行是否成功
    """
    print("\n开始运行 ahc-v201.exe...")
    # 在函数开始时创建一次Path对象，重复使用
    output_path = Path(output_dir)

    # 运行 ahc-v201.exe
    exe_path = output_path / "ahc-v201.exe"

    try:
        # 在临时目录中运行可执行文件
        result = subprocess.run(
            [str(exe_path)],
            cwd=output_dir,
            capture_output=True,
            text=True,
            timeout=60  # 60秒超时
        )

        print(f"返回码: {result.returncode}")
        print(f"标准输出:\n{result.stdout}")
        if result.stderr:
            print(f"标准错误:\n{result.stderr}")

        # 检查是否成功运行
        success = result.returncode == 0
        if success:
            print("\n✓ ahc-v201.exe 运行成功！")
        else:
            print(f"\n✗ ahc-v201.exe 运行失败，返回码: {result.returncode}")

        # 运行完成后删除可执行文件
        _cleanup_executable(exe_path)
        return success

    except subprocess.TimeoutExpired:
        print("\n✗ ahc-v201.exe 运行超时")
        # 即使超时也尝试删除可执行文件
        _cleanup_executable(exe_path)
        return False
    except Exception as e:
        print(f"\n✗ 运行过程中发生错误: {e}")
        # 即使出错也尝试删除可执行文件
        _cleanup_executable(exe_path)
        return False


def _cleanup_executable(exe_path):
    """清理可执行文件"""
    try:
        if exe_path.exists():
            exe_path.unlink()
            print(f"✓ 已删除可执行文件: {exe_path.name}")
    except Exception as e:
        print(f"⚠ 删除可执行文件时出错: {e}")


def check_output_files(output_dir):
    """检查输出文件和日志。

    Args:
        output_dir: 输出目录
    """
    print("\n检查输出文件...")
    # 在函数开始时创建一次Path对象，重复使用
    output_path = Path(output_dir)

    # 检查是否生成了输出文件
    output_files = list(output_path.glob("rs0.*"))
    if output_files:
        print("\n生成的输出文件:")
        for output_file in output_files:
            file_size = output_file.stat().st_size
            print(f"  {output_file.name} ({file_size} 字节)")
    else:
        print("\n未找到输出文件")

    # 检查日志文件（简化输出）
    log_files = ["AHC.LOG", "Path.LOG"]
    for log_file in log_files:
        log_path = output_path / log_file
        if log_path.exists():
            print(f"\n{log_file} 内容:")
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
                if content:
                    print(content[:200])  # 只显示前200个字符
                    if len(content) > 200:
                        print("...")
                else:
                    print("(空文件)")


def run_ahc_simulation(output_dir=None, use_validation=True):
    """运行完整的 AHC 模拟流程。

    Args:
        output_dir: 输出目录，如果为 None 则创建临时目录
        use_validation: 是否使用验证框架，默认为 True

    Returns:
        str: 输出目录路径
    """
    mode_text = "使用验证框架" if use_validation else "传统方式"
    print(f"=== 开始 AHC-V201.exe 执行案例（{mode_text}）===")

    # 创建输出目录
    if output_dir is None:
        project_root = Path("D:\code\DA\enkf-model\pyAHC")
        temp_dir_path = project_root / "temp_hetao_corn_2013"
        temp_dir_path.mkdir(exist_ok=True)
        output_dir = str(temp_dir_path)

    print(f"输出目录: {output_dir}")

    try:
        # 1. 创建模型组件
        print("创建模型组件...")
        model, _, meteo_manager = create_model_components()

        # 2. 生成输入文件
        generate_input_files(model, output_dir)

        # 3. 运行 AHC 可执行文件
        success = run_ahc_executable(output_dir)

        # 4. 检查输出结果
        check_output_files(output_dir)

        if success:
            print("\n✓ AHC 模拟执行成功！")
        else:
            print("\n✗ AHC 模拟执行失败")

        print(f"\n输出目录保留在: {output_dir}")
        return output_dir

    except Exception as e:
        print(f"\n✗ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return output_dir


def main():
    """主函数 - 展示验证框架的使用"""
    print("=== pyAHC 验证框架演示 ===")

    try:
        print("✓ 使用验证框架模式")
        output_directory = run_ahc_simulation(use_validation=True)

        print(f"\n案例执行完成，结果保存在: {output_directory}")
        print("请查看输出目录中的文件以了解模拟结果。")

        return 0

    except Exception as e:
        print(f"✗ 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
