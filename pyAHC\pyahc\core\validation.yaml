CropDevelopmentSettings: &CropDevelopmentSettings
  swcf:
    1:
      - dvs_cf
    2:
      - dvs_ch
      - albedo
      - rsc
      - rsw
  swrd:
    1:
      - rdtb
    2:
      - rdi
      - rri
      - rdc
      - swdmi2rd
    3:
      - rlwtb
      - wrtmax

CropDevelopmentSettingsWOFOST:
  <<: *CropDevelopmentSettings
  idsl:
    1:
      - dlc
      - dlo
    2:
      - dlc
      - dlo
      - vernsat
      - vernbase
      - verndvs
      - verntb

OxygenStress:
  swoxygen:
    1:
      - hlim1
      - hlim2u
      - hlim2l
    2:
      - q10_microbial
      - specific_resp_humus
      - srl
      - swrootradius
      - dry_mat_cont_roots
      - air_filled_root_por
      - spec_weight_root_tissue
      - var_a
      - root_radiuso2
  swrootradius:
    1:
      - dry_mat_cont_roots
      - air_filled_root_por
      - spec_weight_root_tissue
      - var_a
    2:
      - root_radiuso2
  swwrtnonox:
    1:
      - aeratecrit

DroughtStress:
  swdrought:
    1:
      - hlim3h
      - hlim3l
      - hlim4
      - adcrh
      - adcrl
    2:
      - wiltpoint
      - kstem
      - rxylem
      - rootradius
      - kroot
      - rootcoefa
      - swhydrlift
      - rooteff
      - stephr
      - criterhr
      - taccur

SaltStress:
  swsalinity:
    1:
      - saltmax
      - saltslope
    2:
      - salthead

CompensateRWUStress:
  swcompensate:
    1:
      - swstressor
      - alphacrit
    2:
      - swstressor
      - dcritrtz

Interception:
  swinter:
    1:
      - cofab
    2:
      - intertb

CO2Correction:
  swco2:
    1:
      - atmofil
      - co2amaxtb
      - co2efftb
      - co2tratb

Preparation:
  swprep:
    1:
      - zprep
      - hprep
      - maxprepdelay
  swsow:
    1:
      - zsow
      - hsow
      - ztempsow
      - tempsow
      - maxsowdelay
  swgerm:
    1:
      - tsumemeopt
      - tbasem
      - teffmx
    2:
      - tsumemeopt
      - tbasem
      - teffmx
      - hdrygerm
      - hwetgerm
      - zgerm
      - agerm

GrasslandManagement:
  swharvest:
    2:
      - dateharvest
  swdmgrz:
    1:
      - dmgrazing
      - dmgrztb
      - maxdaygrz
    2:
      - tagprest
      - dewrest
      - lsda
  swdmmow:
    1:
      - dmharvest
      - daylastharvest
      - dmlastharvest
      - maxdaymow
    2:
      - dmmowtb
      - dmmowdelay

Meteorology:
  swetr:
    1:
      - swetsine
      - swrain
      - rainflux
    0:
      - alt
      - altw
      - angstroma
      - angstromb
      - swmetdetail
  swrain:
    0:
    1:
      - rainflux
    3:
      - rainfil
  swmetdetail:
    1:
      - nmetdetail

BottomBoundaryBase:
  swbotb:
    1:
      - gwlevel
    2:
      - sw2
    3:
      - sw3
    4:
      - swqhbot
    5:
      - hbot5
  sw2:
    1:
      - sinave
      - sinamp
      - sinmax
    2:
      - qbot
  sw3:
    1:
      - aqave
      - aqamp
      - aqtmax
      - aqper
    2:
      - haquif
  swqhbot:
    1:
      - cofqha
      - cofqhb
      - cofqhc
    2:
      - qtab

DraFile:
  ipos:
    3:
      - khbot
      - zintf
    4:
      - khbot
      - zintf
      - kvtop
      - kvbot
    5:
      - khbot
      - zintf
      - kvtop
      - kvbot
      - geofac

  swintfl:
    1:
      - cofintflb
      - expintflb

Flux:
  swallo:
    1:
      - l

Drainage:
  swdra:
    1:
      - drafile
    2:
      - drafile

HeatFlow:
  swhea:
    1:
      - swcalt
  swcalt:
    1:
      - tampli
      - tmean
      - timref
      - ddamp
    2:
      - soiltextures
      - swtopbhea
      - swbotbhea
  swtopbhea:
    2:
      - tsoilfile
  swbotbhea:
    2:
      - bbctsoil

# Needs a thorough revision and further improvement. Alsoome parameters rely on SWINCO paramaters from
# the soilmoisture section and are not validated.
SoluteTransport:
  swsolu:
    1:
      - cpre
      - cdrain
      - swbotbc
      - swsp # determines the table_miscellaneous columns contant
      - misc
      - ddif
      - tscf
      - swdc
      - swbr
  swbotbc:
    1:
      - cseep
    2:
      - cseeparrtb
  swsp:
    1:
      - frexp
      - cref
  swdc:
    1:
      - gampar
      - rtheta
      - bexp
  swbr:
    1:
      - daquif
      - poros
      - kfsat
      - decsat
      - cdraini
      - inissoil

FixedIrrigation:
  swirfix:
    1:
      - swirgfil
    swirgfil:
      0:
        - irrigevents
      1:
        - irgfile

Crop:
  :
    1:
      - 
      - 
      - cropfiles

GeneralSettings:
  swscre:
    0:
    1:
    3:
  swerror:
    0:
    1:
  swmonth:
    0:
      - period
      - swres
      - swodat
    1:
  swdat:
    0:
    1:
      - outdatin
  swheader:
    0:
    1:
  swyrvar:
    0:
      - datefix
    1:
      - outdat
  swafo:
    0:
    1:
      - critdevmasbal
      - swdiscrvert
    2:
      - critdevmasbal
      - swdiscrvert
  swaun:
    0:
    1:
      - critdevmasbal
      - swdiscrvert
    2:
      - critdevmasbal
      - swdiscrvert
  swdiscrvert:
    0:
    1:
      - numnodnew
      - dznew

  swcsv:
    1:
      - inlist_csv

  swcsv_tz:
    1:
      - inlist_csv_tz

Evaporation:
  swcfbs:
    1:
      - cfbs
  swredu:
    1:
      - cofredbl
      - rsigni
    2:
      - cofredbo

SnowAndFrost:
  swsnow:
    1:
      - snowinco
      - teprrain
      - teprsnow
      - snowcoef
  swfrost:
    1:
      - tfrostst
      - tfrostend

SoilProfile:
  swsophy:
    0:
      - soilhydrfunc
    1:
      - filenamesophy
  swhyst:
    1:
      - tau
    2:
      - tau

SoilMoisture:
  swinco:
    1:
      - head_soildepth
    2:
      - gwli
    3:
      - inifil

SurfaceFlow:
  swpondmx:
    0:
      - pondmx
    1:
      - pondmxtb
  swrunon:
    1:
      - rufil

ScheduledIrrigation:
  schedule:
    1:
      - startirr
      - endirr
      - cirrs
      - isuas
      - tcs
      - dcs
      - dcslim
      - tcsfix
  tcs:
    1:
      - tc1tb
    2:
      - tc2tb
      - phfieldcapacity
    3:
      - tc3tb
      - phfieldcapacity
    4:
      - tc4tb
      - phfieldcapacity
    6:
      - irgthreshold
    7:
      - tc7tb
      - dcrit
      - swcirrthres
    8:
      - tc8tb
      - dcrit
      - swcirrthres
  swcirrthres:
    1:
      - cirrthres
      - perirrsurp
  tcsfix:
    1:
      - irgdayfix
  dcs:
    1:
      - phfieldcapacity
      - tc1tb
      - raithreshold
    2:
      - tc2tb
  dcslim:
    1:
      - irgdepmin
      - irgdepmax

# AHC 特定验证规则扩展
BottomBoundary:
  swbotb:
    1:  # 地下水位选项
      required:
        - gwlevel_data
      validation:
        - "len(gwlevel_data) > 0"
        - "all(isinstance(item, tuple) and len(item) == 3 for item in gwlevel_data)"
    3:  # 深层含水层选项
      required:
        - shape
        - hdrain
        - rimlay
        - aqave
        - aqamp
        - aqtamx
        - aqper
      validation:
        - "shape > 0.0 and shape <= 10.0"
        - "hdrain < 0.0"
        - "rimlay > 0.0"
        - "aqper > 0.0"
    4:  # 地下水位函数选项
      required:
        - cofqha
        - cofqhb
        - cofqhc
      validation:
        - "cofqha is not None"
        - "cofqhb is not None"
        - "cofqhc is not None"

EpicCrop:
  swcf:
    1:  # 作物因子表
      required:
        - cftb
      validation:
        - "cftb is not None"
    2:  # 作物高度表
      required:
        - chtb
        - cfini
      validation:
        - "chtb is not None"
        - "cfini >= 0.0 and cfini <= 500.0"
    3:  # 初始作物高度
      required:
        - cfini
      validation:
        - "cfini >= 0.0 and cfini <= 500.0"

  swrsc:
    1:  # 固定表面阻力
      required:
        - rsc
        - frgmax
        - vpdfr
      validation:
        - "rsc > 0.0"
        - "frgmax >= 0.0 and frgmax <= 1.0"
        - "vpdfr > 0.0"
    2:  # 可变表面阻力
      required:
        - rsctb
        - frgmax
        - vpdfr
      validation:
        - "rsctb is not None"
        - "frgmax >= 0.0 and frgmax <= 1.0"
        - "vpdfr > 0.0"

CtrFile:
  swdra:
    1:  # 启用排水
      required:
        - drfil
      validation:
        - "drfil is not None and len(drfil) > 0"

  swsolu:
    1:  # 启用溶质传输
      required:
        - swstyp
        - bbcfil
      validation:
        - "swstyp in [0, 1]"
        - "bbcfil is not None and len(bbcfil) > 0"

  swhea:
    1:  # 启用热传输
      validation:
        - "True"  # 热传输启用时的基础验证

ObservationPoints:
  obsn:
    validation:
      - "obsn >= 1 and obsn <= 100"
      - "len(observation_depths) == obsn"
      - "all(isinstance(depth, tuple) and len(depth) == 2 for depth in observation_depths)"
      - "all(upper < lower for upper, lower in observation_depths)"
      - "all(upper >= 0 and lower > 0 for upper, lower in observation_depths)"

# 组件间依赖关系验证
ComponentDependencies:
  EpicCrop:
    depends_on:
      - component: "CtrFile"
        conditions:
          - "project is not None"
          - "ssrun is not None"
          - "esrun is not None"

  BottomBoundary:
    depends_on:
      - component: "CtrFile"
        conditions:
          - "swsolu is not None"
          - "swhea is not None"

  ObservationPoints:
    depends_on:
      - component: "CtrFile"
        conditions:
          - "project is not None"

# 参数外部化配置
ParameterExternalization:
  BottomBoundary:
    configurable_params:
      - name: "shape"
        default: 1.0
        range: [0.1, 10.0]
        description: "形状因子"
      - name: "hdrain"
        default: -120.0
        range: [-1000.0, 0.0]
        description: "排水基础"
      - name: "rimlay"
        default: 500.0
        range: [1.0, 10000.0]
        description: "隔水层垂直阻力"
      - name: "aqave"
        default: 0.0
        range: [-1000.0, 1000.0]
        description: "平均水力水头差"
      - name: "aqamp"
        default: 0.0
        range: [0.0, 1000.0]
        description: "水力水头正弦波振幅"
      - name: "aqper"
        default: 365.0
        range: [1.0, 3650.0]
        description: "水力水头正弦波周期"

  EpicCrop:
    configurable_params:
      - name: "tba"
        default: 9.0
        range: [-10.0, 50.0]
        description: "基础温度"
      - name: "topt"
        default: 25.0
        range: [0.0, 50.0]
        description: "最适温度"
      - name: "ruep"
        default: 47.0
        range: [1.0, 100.0]
        description: "辐射利用效率"
      - name: "chmax"
        default: 265.0
        range: [10.0, 1000.0]
        description: "最大冠层高度"

  ObservationPoints:
    configurable_params:
      - name: "repeat_count"
        default: 2
        range: [1, 10]
        description: "重复次数"
      - name: "min_depth_difference"
        default: 1
        range: [1, 100]
        description: "最小深度差异"
      - name: "max_depth"
        default: 1000
        range: [100, 10000]
        description: "最大深度"
