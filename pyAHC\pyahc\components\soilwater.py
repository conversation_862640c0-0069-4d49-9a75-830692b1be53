# mypy: disable-error-code="call-overload, misc"


from pathlib import Path as _Path
from typing import Literal as _Literal, Optional as _Optional, List as _List, Dict as _Dict, Any as _Any

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
    model_validator as _model_validator,
)
from pandas import DataFrame as _DataFrame

from pyahc.components.tables import SOILHYDRFUNC, SOILPROFILE
from pyahc.components.simsettings import RichardsSettings as _RichardsSettings
from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.core.fields import (
    Decimal2f as _Decimal2f,
    Decimal3f as _Decimal3f,
    String as _String,
    Table as _Table,
    PositiveFloat as _PositiveFloat,
    UnitFloat as _UnitFloat,
)
from pyahc.core.valueranges import UNITRANGE as _UNITRANGE
from pyahc.utils.mixins import (
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
    FileMixin as _FileMixin,
)

__all__ = [
    "Evaporation",
    "SnowAndFrost",
    "SoilMoisture",
    "SoilProfile",
    "SoilWater",
    "SurfaceFlow",
    "SOILPROFILE",
    "SOILHYDRFUNC",
]


class Evaporation(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """蒸发设置。

    属性：
        swcfbs (Literal[0, 1]): 使用土壤因子 CFBS 从 ETref 计算 Epot 的开关。
        swredu (Literal[0, 1, 2, 3]): 潜在土壤蒸发量减少方法的开关：

            * 0 - 减少到最大达西通量。
            * 1 - 减少到最大达西通量和最大 Black (1969)。
            * 2 - 减少到最大达西通量和最大 Boesten/Stroosnijder (1986)。
            * 3 - 使用FAO方法减少（适用于浅水位）。

        cfevappond (Optional[Decimal2f]): 当使用 ETref 时，积水情况下的蒸发系数 [0..3]。
        cfbs (Optional[Decimal2f]): 潜在土壤蒸发系数 [0.5..1.5]。
        rsoil (Optional[Decimal2f]): 湿土壤的土壤阻力 [0..1000.0]。
        cofredbl (Optional[Decimal2f]): Black 的土壤蒸发系数 [0..1]。
        rsigni (Optional[Decimal2f]): 重置 Black 方法的最小降雨量 [0..100]。
        cofredbo (Optional[Decimal2f]): Boesten/Stroosnijder 的土壤蒸发系数 [0..1]。
        fk (Optional[Decimal2f]): 衰减因子 [1..8]（当swredu=3时使用）。
        dzsel (Optional[Decimal2f]): 表层蒸发层厚度 [0..40 cm]（当swredu=3时使用）。
        hfcsl (Optional[Decimal2f]): 表层蒸发层田间持水量压头 [-330..-100 cm]（当swredu=3时使用）。
        hwpsl (Optional[Decimal2f]): 表层蒸发层凋萎点压头 [-12000..-6000 cm]（当swredu=3时使用）。
    """

    swcfbs: _Literal[0, 1] | None = None
    swredu: _Literal[0, 1, 2, 3] | None = None
    cfevappond: _Decimal2f | None = _Field(default=None, ge=0, le=3)
    cfbs: _Decimal2f | None = _Field(default=None, ge=0.5, le=1.5)
    rsoil: _Decimal2f | None = _Field(default=None, ge=0, le=1000.0)
    cofredbl: _Decimal2f | None = _Field(default=None, **_UNITRANGE)
    rsigni: _Decimal2f | None = _Field(default=None, ge=0, le=100)
    cofredbo: _Decimal2f | None = _Field(default=None, **_UNITRANGE)
    fk: _Decimal2f | None = _Field(default=None, ge=1, le=8)
    dzsel: _Decimal2f | None = _Field(default=None, ge=0, le=40)
    hfcsl: _Decimal2f | None = _Field(default=None, ge=-330, le=-100)
    hwpsl: _Decimal2f | None = _Field(default=None, ge=-12000, le=-6000)


class SnowAndFrost(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """模型的积雪和霜冻设置。

    属性：
        swice (Literal[0, 1]): 冰融化过程开关。
        swsnow (Literal[0, 1]): 积雪累积和融化计算开关。
        swfrost (Literal[0, 1, 2]): 霜冻情况下减少土壤水流的开关。
        snowinco (Optional[Decimal2f]): 初始雪水当量 [0..1000 cm]。
        snowcoef (Optional[Decimal2f]): 融雪校准因子 [0..10]。
        teprrain (Optional[Decimal2f]): 所有降水均为雨的温度上限 [0..10 oC]。
        teprsnow (Optional[Decimal2f]): 所有降水均为雪的温度下限 [-10..0 oC]。
        tfroststa (Optional[Decimal2f]): 水通量开始减少的土壤温度 (oC) [-10.0..5.0 oC]。
        tfrostend (Optional[Decimal2f]): 水通量结束减少的土壤温度 (oC) [-10.0..5.0 oC]。
        daymels (Optional[Decimal2f]): 从1月1日开始的融化开始日 [0..365]。
        pa (Optional[Decimal2f]): 系数a。
        pb (Optional[Decimal2f]): 系数b。
        pc (Optional[Decimal2f]): 初始未冻深度系数c。
        maxdp (Optional[Decimal2f]): 上下层之间的融化深度 [0..200 cm]。
        gwlmelt (Optional[Decimal2f]): 完全融化日的初始地下水位。
    """

    swice: _Literal[0, 1] | None = None
    swsnow: _Literal[0, 1] | None = None
    swfrost: _Literal[0, 1, 2] | None = None
    snowinco: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    snowcoef: _Decimal2f | None = _Field(default=None, ge=0, le=10)
    teprrain: _Decimal2f | None = _Field(default=None, ge=0, le=10)
    teprsnow: _Decimal2f | None = _Field(default=None, ge=-10, le=0)
    tfroststa: _Decimal2f | None = _Field(default=None, ge=-10, le=5)
    tfrostend: _Decimal2f | None = _Field(default=None, ge=-10, le=5)
    daymels: _Decimal2f | None = _Field(default=None, ge=0, le=365)
    pa: _Decimal2f | None = None
    pb: _Decimal2f | None = None
    pc: _Decimal2f | None = None
    maxdp: _Decimal2f | None = _Field(default=None, ge=0, le=200)
    gwlmelt: _Decimal2f | None = None


class SoilMoisture(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """土壤含水量和水平衡。

    属性：
        swinco (Literal[0, 1, 2, 3]): 初始土壤含水条件类型的开关：

            * 0 - 土壤含水量作为输入。
            * 1 - 压头作为土壤深度的函数。
            * 2 - 每个隔室的压头与初始地下水位处于静水力平衡。
            * 3 - 从上一次 AHC 模拟的输出文件中读取最终压头。

        head_soildepth (Optional[Table]): 包含压头和土壤深度数据的表格。
        gwli (Optional[Decimal2f]): 初始地下水位 [-10000..100 cm]。
        inifil (Optional[str]): 包含初始值的输出文件 *.END 的名称。
        thetai (Optional[List[float]]): 初始土壤含水量数组 [0..1 cm³/cm³]。
    """

    swinco: _Literal[0, 1, 2, 3] | None = None
    head_soildepth: _Table | None = None
    gwli: _Decimal2f | None = _Field(default=None, ge=-10000, le=100)
    inifil: _String | None = None
    thetai: _List[float] | None = _Field(default=None, description="初始土壤含水量数组")


class SoilProfile(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """土壤剖面的垂直离散化、土壤水力函数和土壤持水滞后现象。

    涵盖 .swp 文件的第 4、5、6 和 7 部分。

    属性：
        swsophy (Literal[0, 1]): 解析函数或表格输入的开关

            * 0 - 带有 Mualem - van Genuchten 参数输入的解析函数
            * 1 - 土壤物理表格

        swhyst (Literal[0, 1, 2]): 土壤持水函数的滞后现象

            * 0 - 无滞后
            * 1 - 滞后，初始条件湿润
            * 2 - 滞后，初始条件干燥

        filenamesophy (Optional[str]): 包含每个土壤层土壤水力表格的输入文件名称
        tau (Optional[Decimal2f]): 改变湿润-干燥的最小压头差 [0..1000]。
        swmacro (Literal[0, 1]): 大孔隙优先流的开关
        soilprofile (Table): 包含土壤剖面数据的表格
        soilhydrfunc (Optional[Table]): 包含土壤水力函数的表格
        rds (Optional[Decimal2f]): 土壤允许的最大根深度 [0..1000 cm]。
        swdivd (Literal[0, 1]): 垂直分布开关。
        cofani (Optional[list]): 各土壤层的各向异性因子列表。
    """

    _validation: bool = _PrivateAttr(default=False)

    swsophy: _Literal[0, 1] | None = None
    swhyst: _Literal[0, 1, 2] | None = None
    swmacro: _Literal[0, 1] | None = None
    filenamesophy: _String | None = None
    tau: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    soilprofile: _Table | None = None
    soilhydrfunc: _Table | None = None
    rds: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    swdivd: _Literal[0, 1] | None = None
    cofani: list | None = None

    def model_string(self, project_name: str = "hetao") -> str:
        """生成严格符合V201格式的SOL文件内容。

        Args:
            project_name: 项目名称，用于文件头部
        """
        lines = []

        # 文件头部
        lines.append("*" * 80)
        lines.append(f"* Filename: {project_name}.SOL")
        lines.append("* Contents: AHC 2.0 - Soil hydraulic properties ")
        lines.append("*" * 80)
        lines.append("* col#1 = Residual moisture content, [0...0.4 cm3/cm3, R]")
        lines.append("* col#2 = Saturated moisture content, [0...0.95 cm3/cm3, R]")
        lines.append("* col#3 = Saturated hydraulic conductivity, [0.01...1000 cm/d, R]")
        lines.append("* col#4 = Alpha, main drying curve, [0.0001...1 /cm, R]")
        lines.append("* col#5 = Exponent in hydraulic conductivity function, [-25...25 -, R]")
        lines.append("* col#6 = Parameter n, [1.0001...5 -, R]")
        lines.append("* col#7 = Alpha, main wetting curve, [0.0001...1 /cm, R]")
        lines.append("*" * 93)
        lines.append("")

        # Section 1: Soil hydraulic model
        lines.append("*" * 93)
        lines.append("* Section 1: Soil hydraulic model ")
        lines.append("* ")

        # 使用swsophy属性来决定使用哪种模型，默认使用MVG模型
        use_mvg = True  # 默认使用MVG模型
        if self.swsophy is not None:
            use_mvg = (self.swsophy == 0)  # 0表示使用MVG，1表示使用表格

        swhyvg = 1 if use_mvg else 0
        swhybc = 0  # 通常不使用BC模型

        lines.append(f"  SWhyVG = {swhyvg} ! Check, using Mualem-van Genuchten (MVG) model [Y=1, N=0]")
        lines.append("*")

        if use_mvg:
            lines.append("* ThetaR  ThetaS   Ks     Alpha_d   lamda    n       Alpha_w ")

            # 使用soilhydrfunc表格数据，如果没有则使用默认值
            if self.soilhydrfunc is not None:
                # 从表格中获取数据
                for i in range(len(self.soilhydrfunc.data)):
                    row = self.soilhydrfunc.data[i]
                    # 格式化为固定宽度的字符串
                    line = f"{row['ORES']:.4f}  {row['OSAT']:.4f}  {row['KSATFIT']:.3f}  {row['ALFA']:.4f}  {row['LEXP']:.3f}  {row['NPAR']:.4f}  {row['ALFAW']:.3f}   "
                    lines.append(line)
            else:
                # 使用默认值
                default_soil_data = [
                    "0.0756  0.42    26.904  0.0157  0.500   1.3697  -99.000   ",
                    "0.05    0.4432  27.752  0.0082  0.500   1.6931  -99.000   ",
                    "0.07    0.47    2.4     0.008   0.500   1.60    -99.000   ",
                    "0.04    0.45    29.6    0.010   0.500   1.61    -99.000   "
                ]
                for data_line in default_soil_data:
                    lines.append(data_line)

            lines.append("* End of table")
        else:
            lines.append("* Using soil physical tables - data from external file")

        lines.append("* ")
        lines.append(f"  SWhyBC = {swhybc} ! Check, using Brooks-Corey (BC) model [Y=1, N=0]")
        lines.append("* ")
        lines.append("* Theta-R  ThetaS   Ks     Alpha_d   lamda    n       Alpha_w ")
        lines.append("* End of table")
        lines.append("* End of file " + "*" * 62)

        return "\n".join(lines)
    
    def write_sol(self, path: str, project_name: str = "hetao") -> None:
        """将SOL文件写入指定路径。

        Args:
            path: 输出目录路径
            project_name: 项目名称，用于构建文件名
        """
        import os

        filename = f"{project_name}.SOL"
        filepath = os.path.join(path, filename)

        content = self.model_string(project_name)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)


class SurfaceFlow(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """地表径流设置（积水、径流和入渗）。

    属性：
        swsrom (Literal[1, 2]): 地表径流计算方法的开关

            * 1 - 使用地表水库边界条件
            * 2 - 使用SCS曲线数方法

        swpondmx (Literal[0, 1]): 径流积水阈值变化的开关

            * 0 - 径流积水阈值恒定
            * 1 - 径流积水阈值随时间变化

        swrunon (Literal[0, 1]): 入渗开关

            * 0 - 无入渗
            * 1 - 使用入渗数据

        pondmx (Optional[Decimal2f]): 积水情况下，径流的最小厚度 [0..1000]。
        rsro (Optional[Decimal2f]): 地表径流的排水阻力 [0.001..1.0]。
        rsroexp (Optional[Decimal2f]): 地表径流排水方程的指数 [0.01..10.0]。
        cnv (Optional[Decimal2f]): SCS曲线数值 [20..100]。
        depx (Optional[Decimal2f]): C-N值调整的最大深度 [0..200 cm]。
        rufil (Optional[str]): 入渗文件名。
        pondmxtb (Optional[Table]): 径流最小厚度随时间变化的表格。
    """

    swsrom: _Literal[1, 2] | None = None
    swpondmx: _Literal[0, 1] | None = None
    swrunon: _Literal[0, 1] | None = None
    pondmx: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    rsro: _Decimal3f | None = _Field(default=None, ge=0.001, le=1000.0)
    rsroexp: _Decimal2f | None = _Field(default=None, ge=0.01, le=10.0)
    cnv: _Decimal2f | None = _Field(default=None, ge=20, le=100)
    depx: _Decimal2f | None = _Field(default=None, ge=0, le=200)
    rufil: _String | None = None
    pondmxtb: _Table | None = None


class SoilWater(_ConfigurableComponent, _YAMLValidatorMixin, _FileMixin):
    """重构后的土壤水分组件 - 支持土壤参数验证和外部配置

    新增功能：
    - 土壤水力参数验证（饱和含水量、残余含水量、van Genuchten参数等）
    - 土壤层次一致性验证（确保各参数数组长度与土壤层数一致）
    - 初始条件验证（验证初始含水量和压力水头的合理性）
    - 参数外部化配置支持
    - 物理约束验证（确保土壤参数符合物理约束条件）

    属性：
        surfaceflow (SurfaceFlow): 地表径流设置
        evaporation (Evaporation): 蒸发设置
        soilmoisture (SoilMoisture): 土壤水分设置
        soilprofile (SoilProfile): 土壤剖面设置
        snowandfrost (SnowAndFrost): 积雪和霜冻设置
        richards (_RichardsSettings): Richards方程设置

        # 新增土壤水分参数（外部化配置）
        soil_layers (int): 土壤层数
        soil_hydraulic_data (Optional[DataFrame]): 土壤水力参数数据
        initial_water_content (Optional[List[float]]): 初始含水量
        initial_pressure_head (Optional[List[float]]): 初始压力水头
    """

    # 原有组件属性
    surfaceflow: SurfaceFlow | None = None
    evaporation: Evaporation | None = None
    soilmoisture: SoilMoisture | None = None
    soilprofile: SoilProfile | None = None
    snowandfrost: SnowAndFrost | None = None
    richards: _Optional['_RichardsSettings'] = None

    # 新增土壤层数（外部化配置）
    soil_layers: int = _Field(default=4, ge=1, le=100, description="土壤层数")

    # 新增土壤水力参数（外部化配置）
    soil_hydraulic_data: _Optional[_DataFrame] = _Field(default=None, description="土壤水力参数数据")

    # 新增初始条件（外部化配置）
    initial_water_content: _Optional[_List[float]] = _Field(default=None, description="初始含水量")
    initial_pressure_head: _Optional[_List[float]] = _Field(default=None, description="初始压力水头")

    @_model_validator(mode="after")
    def validate_soil_parameters(self):
        """验证土壤参数的合理性和一致性"""
        if not self._validation:
            return self

        # 验证土壤水力参数
        if self.soil_hydraulic_data is not None:
            self._validate_hydraulic_parameters()

        # 验证初始条件与土壤层数的一致性
        self._validate_initial_conditions()

        return self

    def _validate_hydraulic_parameters(self):
        """验证土壤水力参数"""
        data = self.soil_hydraulic_data

        # 检查必需的土壤参数列
        required_columns = ['DEPTH', 'THETA_SAT', 'THETA_RES', 'ALPHA', 'N', 'KS']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"Missing required soil hydraulic columns: {missing_columns}")

        # 验证参数范围和物理约束
        if (data['THETA_SAT'] <= data['THETA_RES']).any():
            raise ValueError("THETA_SAT must be > THETA_RES for all layers")

        if (data['ALPHA'] <= 0).any():
            raise ValueError("ALPHA values must be > 0")

        if (data['N'] <= 1).any():
            raise ValueError("N values must be > 1")

        if (data['KS'] <= 0).any():
            raise ValueError("KS values must be > 0")

        # 验证含水量范围
        if (data['THETA_SAT'] > 1.0).any() or (data['THETA_SAT'] < 0).any():
            raise ValueError("THETA_SAT values must be between 0 and 1")

        if (data['THETA_RES'] > 1.0).any() or (data['THETA_RES'] < 0).any():
            raise ValueError("THETA_RES values must be between 0 and 1")

    def _validate_initial_conditions(self):
        """验证初始条件与土壤层数的一致性"""
        if self.initial_water_content and len(self.initial_water_content) != self.soil_layers:
            raise ValueError(
                f"Initial water content length ({len(self.initial_water_content)}) "
                f"must equal soil layers ({self.soil_layers})"
            )

        if self.initial_pressure_head and len(self.initial_pressure_head) != self.soil_layers:
            raise ValueError(
                f"Initial pressure head length ({len(self.initial_pressure_head)}) "
                f"must equal soil layers ({self.soil_layers})"
            )

        # 验证初始含水量范围
        if self.initial_water_content:
            for i, theta in enumerate(self.initial_water_content):
                if theta < 0 or theta > 1:
                    raise ValueError(f"Initial water content at layer {i+1} must be between 0 and 1, got {theta}")

    def model_string(self, project_name: str = "hetao") -> str:
        """生成SWP文件内容，使用组件属性值而非硬编码数值。

        Args:
            project_name: 项目名称，用于文件头部
        """
        lines = []

        # 文件头部 - 按照V201格式
        lines.append(f"* Project: {project_name}")
        lines.append(f"* Filename: {project_name}.SWP")
        lines.append("* Contents: AHC 2.0 - Soil water and profile data")
        lines.append("***********************************************************************************************")
        lines.append("* Case: Water and solute transport in the Huinong area.")
        lines.append("***********************************************************************************************")
        lines.append("")

        # Section 1: Surface runoff - 使用 surfaceflow 组件的属性
        lines.append("***********************************************************************************************")
        lines.append("* Section 1: Surface runoff  * Section 1: Ponding")
        lines.append("*")

        # 使用 surfaceflow 组件的属性，如果没有设置则使用默认值
        swsrom = 1  # 默认值
        pondmx = 20  # 默认值
        rsro = 20.0  # 默认值
        rsroexp = 1.0  # 默认值
        cnv = 67  # 默认值
        depx = None  # 默认为空值，与标准案例一致

        if self.surfaceflow:
            if self.surfaceflow.swsrom is not None:
                swsrom = self.surfaceflow.swsrom
            if self.surfaceflow.pondmx is not None:
                pondmx = self.surfaceflow.pondmx
            if self.surfaceflow.rsro is not None:
                rsro = self.surfaceflow.rsro
            if self.surfaceflow.rsroexp is not None:
                rsroexp = self.surfaceflow.rsroexp
            if self.surfaceflow.cnv is not None:
                cnv = self.surfaceflow.cnv
            if self.surfaceflow.depx is not None:
                depx = self.surfaceflow.depx

        lines.append(f"  SWSROM = {swsrom} ! Choise, method for calculation of surface runoff")
        lines.append("*              1 = use the surface reservoir boundary condition")
        lines.append("*              2 = use the SCS curve number method ")
        lines.append("*")
        lines.append(f"  PONDMX = {pondmx} ! Maximum thickness of ponding water layer [0..1000 cm, R]")
        lines.append(f"  RSRO = {rsro} ! Drainage resistance for surface runoff [0.001..1.0 d, R]")
        lines.append(f"  ROEXP = {rsroexp} ! Exponent in drainage equation of surface runoff [0.1..10.0 -, R]")
        lines.append("*")
        lines.append("* If SWSROM = 2,then specify")
        lines.append(f"  CNV = {cnv} ! Curve number value [20...100,R]")
        depx_str = f"{depx}" if depx is not None else ""
        lines.append(f"  DEPX = {depx_str} ! Maximum depth for the adjustment of C-N value [0...200 cm, R]")
        lines.append("****************** *****************************************************************************")
        lines.append("")

        # Section 2: Soil evaporation - 使用 evaporation 组件的属性
        lines.append("* Section 2: soil evaporation")

        # 使用 evaporation 组件的属性
        swcfbs = 0  # 默认值
        cfbs = 1.4  # 默认值
        swredu = 1  # 默认值
        cofredbl = 0.6  # 默认值
        rsigni = 0.50  # 默认值
        fk = 4.0  # 默认值
        dzsel = 15  # 默认值
        hfcsl = -100  # 默认值
        hwpsl = -8000  # 默认值

        if self.evaporation:
            if self.evaporation.swcfbs is not None:
                swcfbs = self.evaporation.swcfbs
            if self.evaporation.cfbs is not None:
                cfbs = self.evaporation.cfbs
            if self.evaporation.swredu is not None:
                swredu = self.evaporation.swredu
            if self.evaporation.cofredbl is not None:
                cofredbl = self.evaporation.cofredbl
            if self.evaporation.rsigni is not None:
                rsigni = self.evaporation.rsigni
            if self.evaporation.fk is not None:
                fk = self.evaporation.fk
            if self.evaporation.dzsel is not None:
                dzsel = self.evaporation.dzsel
            if self.evaporation.hfcsl is not None:
                hfcsl = self.evaporation.hfcsl
            if self.evaporation.hwpsl is not None:
                hwpsl = self.evaporation.hwpsl

        lines.append(f"  SWCFBS = {swcfbs} ! Use CFBS to calculate ES0 from ET0 [Y=1, N=0]")
        lines.append("* If SWCFBS = 1 specify CFBS:")
        lines.append(f"  CFBS   = {cfbs} ! Conversion factor [0.5..1.5, R]")
        lines.append("*")
        lines.append(f"  SWREDU = {swredu} ! Switch, method for reduction of soil evaporation")
        lines.append("*              0 = reduction to maximum Darcy flux")
        lines.append("*              1 = reduction to maximum Darcy flux and to maximum Black (1969),")
        lines.append("*              2 = reduction to maximum Darcy flux and to maximum Bo/Str. (1986),")
        lines.append("*              3 = reduction using FAO method [good for shallow water table")
        lines.append("*If SWREDU 1 or 2 then specify:")
        lines.append(f"  COFRED = {cofredbl} ! soil evaporation coefficient of Black, [0..1 cm/d1/2, R],")
        lines.append("*                                 or Boesten/Stroosnijder, [0..1 cm1/2, R]")
        lines.append(f" RSIGNI = {rsigni:.2f} ! Minimum rainfall to reset models Black and Bo/Str., [0..1 cm/d, R]")
        lines.append("*If SWREDU=3 then specify:")
        lines.append(f" FK = {fk} ! decline factor [1...8, R]")
        lines.append(f" DZSEL = {int(dzsel) if dzsel == int(dzsel) else dzsel} ! Thickness for surface evaporation layer [0...40 cm,R]")
        lines.append(f" hFCSL= {int(hfcsl) if hfcsl == int(hfcsl) else hfcsl} ! h at field capacity for surface evaporation layer [-330...-100 cm, R]")
        lines.append(f" hWPSL = {hwpsl} ! h at wilting point for surface evaporation layer  [-12000...-6000 cm, R]")
        lines.append("***********************************************************************************************")
        lines.append("")

        # Section 3: Time discretization - 使用 richards 组件的属性
        lines.append("***********************************************************************************************")
        lines.append("* Section 3: Time discretization of Richards equation")
        lines.append("*")

        # 使用 richards 组件的属性
        dtmin = 1.00E-04  # 默认值
        dtmax = 0.2  # 默认值
        thetol = 0.009  # 默认值
        swkmean = 1  # 默认值

        if self.richards:
            if self.richards.dtmin is not None:
                dtmin = self.richards.dtmin
            if self.richards.dtmax is not None:
                dtmax = self.richards.dtmax
            if self.richards.swkmean is not None:
                swkmean = self.richards.swkmean

        lines.append(f"  DTMIN  = {dtmin:.2E} ! Minimum timestep [1.0E-8..0.1 d, R]")
        lines.append(f"  DTMAX  = {dtmax} ! Maximum timestep [0.01..0.5 d, R]")
        lines.append(f"  THETOL = {thetol} ! Max. dif. water content between iterations, [1.E-5..0.01 cm3/cm3, R]")
        lines.append(f"  SWMHC= {swkmean}! Switch, calculate the mean hydraulic conductivity between two nodes [1..6,I]")
        lines.append("*                     1 = Arithmetric mean")
        lines.append("*                     2 = Harmonic mean")
        lines.append("*                     3 = Geometric mean/Logarithmic mean")
        lines.append("*                     4 = Weighted arithmetric mean")
        lines.append("*                     5 = Weighted harmonic mean")
        lines.append("*                     6 = Weighted geometric mean")
        lines.append("***********************************************************************************************")
        lines.append("")

        return self._generate_remaining_sections(lines)

    def _generate_remaining_sections(self, lines: list) -> str:
        """生成剩余的部分，保持与原始格式一致。"""

        # Section 4: Spatial discretization - 使用 soilprofile 组件的属性
        lines.append("***********************************************************************************************")
        lines.append("* Section 4: Spatial discretization")
        lines.append("*")

        # 使用 soilprofile 组件的属性，如果没有设置则使用默认值
        numlay = 4  # 默认值
        numnod = 300  # 默认值
        botcom = [40, 130, 240, 300]  # 默认值
        dz_values = [1.0] * 300  # 默认值

        if self.soilprofile and self.soilprofile.soilprofile is not None:
            # 从soilprofile表格中获取数据
            profile_data = self.soilprofile.soilprofile.data
            if profile_data:
                numlay = len(profile_data)
                # 计算总节点数和底部节点
                total_nodes = 0
                botcom = []
                dz_list = []

                for row in profile_data:
                    ncomp = row['NCOMP']
                    hcomp = row['HCOMP']
                    total_nodes += ncomp
                    botcom.append(total_nodes)
                    # 为每个子层添加厚度值
                    dz_list.extend([hcomp] * ncomp)

                numnod = total_nodes
                dz_values = dz_list

        lines.append(f"  NUMLAY = {numlay} ! Number of soil layers,[1..10, I]")
        lines.append(f"  NUMNOD = {numnod} ! Number of soil compartments, [1..500, I] ")
        lines.append("*")
        lines.append("* List compartment number at bottom of each soil layer (max 5), [1..40, I]")
        lines.append(" BOTCOM = ")
        botcom_str = "    ".join(str(val) for val in botcom) + "    "
        lines.append(f" {botcom_str}")
        lines.append("*")
        lines.append("* List thickness of each soil compartment (max 40), [0..500 cm, R]:")
        lines.append("  DZ = ")

        # 添加DZ数据，每行10个，格式与原始文件一致
        for i in range(0, len(dz_values), 10):
            line_values = dz_values[i:i+10]
            lines.append(" " + "    ".join(f"{val}" for val in line_values) + "    ")
        lines.append("* End of table")
        lines.append("***********************************************************************************************")
        lines.append(" ")

        # 继续添加其他部分...
        self._add_soil_hydraulic_section(lines)
        self._add_hysteresis_section(lines)
        self._add_scaling_section(lines)
        self._add_preferential_flow_sections(lines)
        self._add_initial_moisture_section(lines)
        self._add_snow_frost_section(lines)

        return "\n".join(lines)

    def _add_soil_hydraulic_section(self, lines: list):
        """添加土壤水力函数部分。"""
        lines.extend([
            "***********************************************************************************************",
            "* Section 5: Soil hydraulic fucntions and maximum rooting depth",
            "* Specify for each soil layer the soil texture (g/g mineral parts) and",
            "* the organic matter content (g/g dry soil), Bulk density (g/cm3)",
            "*   PSand    PSilt    PClay   OrgMat  Bdens"
        ])

        # 使用soilhydrfunc表格数据，如果没有则使用默认值
        if self.soilprofile and self.soilprofile.soilhydrfunc is not None:
            # 从表格中获取数据
            for i in range(len(self.soilprofile.soilhydrfunc.data)):
                row = self.soilprofile.soilhydrfunc.data[i]
                # 计算土壤质地（这里使用默认值，实际应该从数据中计算）
                psand = 0.30  # 默认砂土比例
                psilt = 0.60  # 默认粉土比例
                pclay = 0.10  # 默认粘土比例
                orgmat = 0.016  # 默认有机质含量
                bdens = row.get('BDENS', 1.40)  # 容重

                line = f" {psand:.4f}    {psilt:.4f}    {pclay:.4f}    {orgmat:.4f}    {bdens:.2f}      "
                lines.append(line)
        else:
            # 使用默认值
            default_texture_data = [
                " 0.3037    0.5751    0.1211    0.0163    1.40      ",
                " 0.2949    0.5958    0.1092    0.0163    1.40      ",
                " 0.0384    0.7514    0.2102    0.0163    1.40      ",
                " 0.34      0.5832    0.0768    0.0163    1.40      "
            ]
            lines.extend(default_texture_data)

        # 最大根深度
        rds = 90.0  # 默认值
        if self.soilprofile and self.soilprofile.rds is not None:
            rds = self.soilprofile.rds

        lines.extend([
            "* End of table",
            "*",
            f"  RDS    = {rds} ! Maximum rooting depth allowed by the soil [0..1000 cm, R]",
            "***********************************************************************************************",
            ""
        ])

    def _add_hysteresis_section(self, lines: list):
        """添加滞后现象部分 - 使用 soilprofile 组件的属性。"""
        lines.extend([
            "***********************************************************************************************",
            "* Section 6: Hysteresis of soil water retention function",
            "*"
        ])

        # 使用 soilprofile 组件的属性
        swhyst = 0  # 默认值
        tau = 0.2  # 默认值

        if self.soilprofile:
            if self.soilprofile.swhyst is not None:
                swhyst = self.soilprofile.swhyst
            if self.soilprofile.tau is not None:
                tau = self.soilprofile.tau

        lines.extend([
            f"  SWHYST = {swhyst} ! Switch, hysteresis:",
            "*              0 = no hysteresis",
            "*              1 = hysteresis, initial condition wetting",
            "*              2 = hysteresis, initial condition drying",
            "*"
        ])

        if swhyst in [1, 2]:
            lines.append(f"  TAU    = {tau} ! Minimum press. head difference to change wetting-drying, [0..1 cm, R]")
        else:
            lines.append("* If SWHYST 1 or 2 then specify:")
            lines.append("  TAU    =  ! Minimum press. head difference to change wetting-drying, [0..1 cm, R]")

        lines.extend([
            "***********************************************************************************************",
            ""
        ])

    def _add_scaling_section(self, lines: list):
        """添加相似介质缩放部分。"""
        # 使用默认值
        swscal = 0  # 默认值
        nscale = 1  # 默认值
        isclay = 1  # 默认值
        fscale = []  # 默认值

        lines.extend([
            "***********************************************************************************************",
            "* Section 7: similar media scaling of soil hydraulic functions",
            "*",
            f" SWSCAL = {swscal} ! Switch, similar media scaling [Y=1, N=0]",
            "*"
        ])

        if swscal == 1:
            lines.extend([
                "* If SWSCAL = 1 specify: ",
                f"  NSCALE = {nscale} ! Number of runs [1..30, I]",
                f"  ISCLAY = {isclay} ! Soil layer number to which scaling is applied [1..5,I] ",
                "*",
                "* List scaling factors of each run (max. = 30), [0..100, R]",
                "  FSCALE = "
            ])

            # 输出FSCALE数据
            if fscale:
                fscale_str = "    ".join(f"{val}" for val in fscale)
                lines.append(f" {fscale_str}")
            else:
                lines.append(" ")
        else:
            lines.extend([
                "* If SWSCAL = 1 specify:",
                f"  NSCALE = {nscale} ! Number of runs [1..30, I]",
                f"  ISCLAY = {isclay} ! Soil layer number to which scaling is applied [1..5,I] ",
                "*",
                "* List scaling factors of each run (max. = 30), [0..100, R]",
                "  FSCALE = ",
                " "
            ])

        lines.extend([
            "***********************************************************************************************",
            ""
        ])

    def _add_preferential_flow_sections(self, lines: list):
        """添加优先流部分。"""
        # Section 8: Preferential flow (immobile water)
        lines.extend([
            "***********************************************************************************************",
            "* Section 8: Preferential flow due to soil volumes with immobile water",
            "*",
            "  SWMOBI = 0 ! Switch, preferential flow due to immobile water [Y=1, N=0]",
            "*",
            "* If SWMOBI = 1, specify mobile fraction as function of log -h for each soil layer: ",
            "* PF1    first datapoint;  log -h [0..5, R]",
            "* FM1    first datapoint;  mobile fraction (1.0 = totally mobile), [0..1 -, R]",
            "* PF2    second datapoint; log h [0..5,  R]",
            "* FM2    second datapoint; mobile fraction (1.0 = totally mobile), [0..1 -, R]",
            "* Also specify volumetric water content in immobile soil volume (THETIM), [0..0.3 -, R]",
            "*   PF1    FM1    PF2    FM2   THETIM ",
            " 0.0     0.4     3.0     0.4     0.02    ",
            " 0.0     1.0     3.0     1.0     0.02    ",
            " 0.0     1.0     3.0     1.0     0.02    ",
            " 0.0     1.0     3.0     1.0     0.02    ",
            " 0.0     1.0     3.0     1.0     0.02    ",
            "* End of table",
            "***********************************************************************************************",
            ""
        ])

        # Section 9: Preferential flow (cracks)
        lines.extend([
            "***********************************************************************************************",
            "* Section 9: Preferential flow due to soil cracks",
            "*",
            "SWCRACK = 0 ! Switch, soil cracks [Y=1, N=0]",
            "*",
            "* If SWCRACK = 1, specify: ",
            "  SHRINA  = 1.0 ! Void ratio at zero water content [-]",
            "  MOISR1  = 1.2 ! Moisture ratio at trans. residual ---> normal shrink [0..5 cm3/cm3,R]",
            "  MOISRD  = 0.2 ! Structural shrinkage [0..1 cm3/cm3, R]",
            "  ZNCRACK = -10.0 ! Depth at which crack area of soil surface is calculated [-100..0 cm, R]",
            "  GEOMF   = 5 ! Geometry factor (3 = isotropic shrinkage), [0..100, R]",
            "  DIAMPOL = 5 ! Diameter soil matrix polygon [0..100 cm, R]",
            "  RAPCOEF = 200 ! Rate coef. bypass flow from cracks to surface water [0..10000 /d, R]",
            "  DIFDES  = 160 ! Effective lateral solute diffusion coefficient [0..10000 /d, R]",
            "*",
            "* If SWCRACK = 1, specify also crit. water content of each soil layer (max. 5), [0..1, R]",
            "* if actual water content becomes less than critical water content, cracks are formed",
            "  THETCR = ",
            " 0.1    0.12   0.1    0.1    0.08   ",
            "***********************************************************************************************",
            " "
        ])

        # Section 10: Vertical drainage flux distribution
        lines.extend([
            "***********************************************************************************************",
            "* Section 10: vertical distribution drainage flux in saturated part soil column",
            "*"
        ])

        # 使用默认值或从组件获取
        swdivd = 1  # 默认值
        cofani = [0.2, 0.2, 0.2, 0.2]  # 默认值

        if self.soilprofile:
            if self.soilprofile.swdivd is not None:
                swdivd = self.soilprofile.swdivd
            if self.soilprofile.cofani is not None:
                cofani = self.soilprofile.cofani
            elif self.soilprofile.soilprofile is not None:
                # 如果有soilprofile数据，根据层数调整cofani
                profile_data = self.soilprofile.soilprofile.data
                if profile_data:
                    num_layers = len(profile_data)
                    cofani = [0.2] * num_layers  # 为每层设置默认值

        lines.extend([
            f"  SWDIVD = {swdivd} ! Switch, apply vertical distribution [Y=1, N=0]",
            "*",
            "* If SWDIVD = 1, specify anisotropy factor (vertical/horizontal saturated hydraulic",
            "*                conductivity) for each soil layer (max. 5), [0..1000 -, R]:",
            "  COFANI = "
        ])

        # 输出COFANI数据
        cofani_str = "    ".join(f"{val}" for val in cofani) + "                                              "
        lines.append(f" {cofani_str}")

        lines.extend([
            "***********************************************************************************************",
            ""
        ])

    def _add_initial_moisture_section(self, lines: list):
        """添加初始水分条件部分 - 使用 soilmoisture 组件的属性。"""
        lines.extend([
            "***********************************************************************************************",
            "* Section 11: Initial moisture condition",
            "*"
        ])

        # 使用 soilmoisture 组件的属性
        swinco = 0  # 默认值
        gwli = -300  # 默认值
        qsti = ""  # 默认值设置为空，严格按照标准案例文件

        if self.soilmoisture:
            if self.soilmoisture.swinco is not None:
                swinco = self.soilmoisture.swinco
            if self.soilmoisture.gwli is not None:
                gwli = self.soilmoisture.gwli

        lines.extend([
            f"  SWINCO = {swinco} ! Switch, type of initial moisture condition:",
            "*              0 = soil water content (theta) T_HETAI",
            "*              1 = pressure head of each compartment is input, H_I",
            "*              2 = pressure head of each compartment is in hydrostatic equilibrium with",
            "*                  initial groundwater table,",
            "* If SWINCO = 0, specify initial soil water content (max 500), [0-1.0 cm3/cm3, R] ",
            "* If SWINCO = 1, specify initial h (max 500), [-1.E10..200 cm, R] ",
            " THETAI = "
        ])

        # 生成THETAI数据 - 根据土壤层数和节点数生成合理的初始含水量分布
        if swinco == 0:
            # 如果提供了thetai数组，使用它；否则使用默认分布
            if self.soilmoisture and self.soilmoisture.thetai is not None:
                thetai_values = self.soilmoisture.thetai
            else:
                thetai_values = self._generate_initial_moisture_distribution()

            # 输出THETAI数据，每行10个值
            for i in range(0, len(thetai_values), 10):
                row_values = thetai_values[i:i+10]
                line_values = [f"{val:.2f}" for val in row_values]
                lines.append(" " + "      ".join(line_values) + "      ")

        lines.extend([
            "*",
            "* If SWINCO = 2, specify:",
            f"  GWLI   = {gwli} ! Initial groundwater level, [-5000..100 cm, R]",
            f"  QSTI   = {qsti} ! water flux under steady-state condition at initial state [-3..3 cm/d, R]",
            "*",
            "***********************************************************************************************",
            ""
        ])

    def _generate_initial_moisture_distribution(self) -> list:
        """生成初始含水量分布。"""
        # 默认300个节点的含水量分布
        thetai_values = []

        # 根据深度生成含水量梯度
        total_nodes = 300  # 默认节点数

        # 如果有soilprofile数据，使用实际节点数
        if self.soilprofile and self.soilprofile.soilprofile is not None:
            profile_data = self.soilprofile.soilprofile.data
            if profile_data:
                total_nodes = sum(row['NCOMP'] for row in profile_data)

        # 生成分层含水量
        for i in range(total_nodes):
            depth_ratio = i / total_nodes
            if depth_ratio < 0.1:  # 表层 0-10%
                theta = 0.26
            elif depth_ratio < 0.3:  # 浅层 10-30%
                theta = 0.30 + (depth_ratio - 0.1) * 0.1  # 0.30-0.32
            elif depth_ratio < 0.6:  # 中层 30-60%
                theta = 0.32 + (depth_ratio - 0.3) * 0.2  # 0.32-0.38
            elif depth_ratio < 0.8:  # 深层 60-80%
                theta = 0.38 + (depth_ratio - 0.6) * 0.15  # 0.38-0.41
            else:  # 底层 80-100%
                theta = 0.41 + (depth_ratio - 0.8) * 0.1  # 0.41-0.43

            thetai_values.append(theta)

        return thetai_values

    def _add_snow_frost_section(self, lines: list):
        """添加积雪和霜冻部分 - 使用 snowandfrost 组件的属性。"""
        lines.extend([
            "***********************************************************************************************",
            "* Section 12: Ice melting process in early period",
            "*"
        ])

        # 使用 snowandfrost 组件的属性
        swice = 0  # 默认值
        daymels = 73.00  # 默认值
        pa = 0.035  # 默认值
        pb = 2.000  # 默认值
        pc = 10.00  # 默认值
        maxdp = 80.0  # 默认值
        gwlmelt = -117.8  # 默认值
        swsnow = 0  # 默认值
        snowinco = 0.0  # 默认值
        snowcoef = 0.3  # 默认值
        swfrost = 0  # 默认值

        if self.snowandfrost:
            if self.snowandfrost.swice is not None:
                swice = self.snowandfrost.swice
            if self.snowandfrost.daymels is not None:
                daymels = self.snowandfrost.daymels
            if self.snowandfrost.pa is not None:
                pa = self.snowandfrost.pa
            if self.snowandfrost.pb is not None:
                pb = self.snowandfrost.pb
            if self.snowandfrost.pc is not None:
                pc = self.snowandfrost.pc
            if self.snowandfrost.maxdp is not None:
                maxdp = self.snowandfrost.maxdp
            if self.snowandfrost.gwlmelt is not None:
                gwlmelt = self.snowandfrost.gwlmelt
            if self.snowandfrost.swsnow is not None:
                swsnow = self.snowandfrost.swsnow
            if self.snowandfrost.snowinco is not None:
                snowinco = self.snowandfrost.snowinco
            if self.snowandfrost.snowcoef is not None:
                snowcoef = self.snowandfrost.snowcoef
            if self.snowandfrost.swfrost is not None:
                swfrost = self.snowandfrost.swfrost

        lines.extend([
            f"  swice   = {swice} ! Switch, ice melting process [Y=1, N=0]",
            "* If swice = 1, specify:    y=a*x^b+c",
            f"  daymels = {daymels} ! start day of thaw from Jan 1st=0 [d]",
            f"  pa      = {pa} ! coefficient a [-]",
            f"  pb      = {pb} ! coefficient b [-]",
            f"  pc      = {pc} ! coefficient c for initial unfrozen depth [-]",
            f"  maxdp   = {maxdp} ! melting depth between up and below layer [cm]",
            f"  gwlmelt = {gwlmelt} ! gwl at initial of fully melting day [d]",
            "*",
            "***********************************************************************************************",
            " ",
            "***********************************************************************************************",
            "* Section 13: Snow and frost",
            "*",
            f"  SWSNOW  = {swsnow} ! Switch, calculate snow accumulation and melt. [Y=1, N=0]",
            "*"
        ])

        if swsnow == 1:
            lines.extend([
                "* If SWSNOW = 1, then specify initial snow water equivalent and snowmelt factor",
                f"  SNOWINCO= {snowinco} ! the initial SWE (Snow Water Equivalent), [0.0...1000.0 cm, R]",
                f"  SNOWCOEF= {snowcoef} ! calibration factor for snowmelt, [0.0...10.0 -, R]"
            ])
        else:
            lines.extend([
                "* If SWSNOW = 1, then specify initial snow water equivalent and snowmelt factor",
                f"  SNOWINCO= {snowinco} ! the initial SWE (Snow Water Equivalent), [0.0...1000.0 cm, R]",
                f"  SNOWCOEF= {snowcoef} ! calibration factor for snowmelt, [0.0...10.0 -, R]"
            ])

        lines.extend([
            "*",
            f"  SWFROST= {swfrost}! Switch, in case of frost: stop soil water flow [0..2,I]",
            "*                     0 = None forst treatment",
            "*                     1 = Reduce water flow in case of frost",
            "*                     2 = Simulate soil freezing and thawing",
            "*",
            "* End of file *********************************************************************************"
        ])

    def write_swp(self, filepath: str | _Path, project_name: str = "hetao"):
        """将SoilWater组件序列化为SWP文件。

        参数:
            filepath (str | Path): 文件保存路径
            project_name (str): 项目名称，默认为"hetao"
        """
        filepath = _Path(filepath)
        if filepath.is_dir():
            filepath = filepath / f"{project_name}.SWP"

        content = self.model_string(project_name)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)


# 解决前向引用问题
SoilWater.model_rebuild()
